/**
 * 基础使用示例
 * 展示如何在 Electron 应用中使用 IPC 通信架构
 */

// ============= 主进程 (main.ts) =============

import { app, BrowserWindow, ipcMain } from 'electron'
import { createIPCServer, exposeBridgeAPI } from '../src/core/ipc'

class MainProcess {
  private ipcServer: any
  private mainWindow: BrowserWindow | null = null

  async initialize() {
    // 创建 IPC 服务端
    this.ipcServer = createIPCServer({
      processType: 'main',
      enableLogging: true,
      enableMetrics: true,
      defaultTimeout: 30000
    })

    // 注册服务端方法
    this.registerMethods()

    // 设置 IPC 监听
    this.setupIpcListeners()

    // 创建窗口
    await this.createWindow()
  }

  private registerMethods() {
    // 注册文件操作方法
    this.ipcServer.registerMethod('readFile', async (filePath: string) => {
      const fs = await import('fs/promises')
      return await fs.readFile(filePath, 'utf-8')
    })

    // 注册系统信息方法
    this.ipcServer.registerMethod('getSystemInfo', async () => {
      const os = await import('os')
      return {
        platform: os.platform(),
        arch: os.arch(),
        cpus: os.cpus().length,
        memory: os.totalmem()
      }
    })

    // 注册数据库操作方法
    this.ipcServer.registerMethod('queryDatabase', async (query: string, params: any[]) => {
      // 模拟数据库查询
      await new Promise(resolve => setTimeout(resolve, 100))
      return {
        query,
        params,
        results: [{ id: 1, name: 'Test' }]
      }
    })
  }

  private setupIpcListeners() {
    // 监听来自渲染进程的消息
    ipcMain.on('ipc-communication', (event, message) => {
      this.ipcServer.handleMessage(message, {
        sender: event.sender,
        timestamp: Date.now()
      })
    })

    // 监听服务端事件
    this.ipcServer.on('error', (error: any, message: any) => {
      console.error('IPC Server Error:', error)
    })
  }

  private async createWindow() {
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: require.resolve('./preload.js')
      }
    })

    // 注册窗口到 IPC 服务端
    this.ipcServer.registerWindow(this.mainWindow)

    await this.mainWindow.loadFile('index.html')

    // 定期广播系统状态
    setInterval(() => {
      this.ipcServer.broadcast('systemStatus', {
        timestamp: Date.now(),
        memory: process.memoryUsage(),
        uptime: process.uptime()
      })
    }, 5000)
  }

  dispose() {
    this.ipcServer?.dispose()
  }
}

// 启动应用
const mainProcess = new MainProcess()

app.whenReady().then(() => {
  mainProcess.initialize()
})

app.on('window-all-closed', () => {
  mainProcess.dispose()
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// ============= 预加载脚本 (preload.ts) =============

import { exposeBridgeAPI } from '../src/core/ipc'

// 暴露 IPC 桥接 API 到渲染进程
exposeBridgeAPI()

// ============= 渲染进程 (renderer.ts) =============

// 声明全局 IPC 桥接 API
declare global {
  interface Window {
    ipcBridge: {
      call: <T>(method: string, params?: any[], options?: any) => Promise<T>
      subscribe: <T>(event: string, listener: (data: T) => void, options?: any) => string
      publish: <T>(event: string, data?: T, namespace?: string) => void
      uploadFile: (file: File, options?: any) => Promise<string>
      // ... 其他方法
    }
  }
}

class RendererApp {
  private subscriptions: string[] = []

  async initialize() {
    // 订阅系统状态事件
    const statusSubscription = window.ipcBridge.subscribe('systemStatus', (status: any) => {
      this.updateSystemStatus(status)
    })
    this.subscriptions.push(statusSubscription)

    // 设置 UI 事件监听
    this.setupUIEventListeners()

    // 加载初始数据
    await this.loadInitialData()
  }

  private async loadInitialData() {
    try {
      // 获取系统信息
      const systemInfo = await window.ipcBridge.call('getSystemInfo')
      this.displaySystemInfo(systemInfo)

      // 查询数据库
      const dbResults = await window.ipcBridge.call('queryDatabase',
        'SELECT * FROM users WHERE active = ?', [true]
      )
      this.displayDatabaseResults(dbResults)

    } catch (error) {
      console.error('Failed to load initial data:', error)
    }
  }

  private setupUIEventListeners() {
    // 文件读取按钮
    const readFileBtn = document.getElementById('readFileBtn')
    readFileBtn?.addEventListener('click', async () => {
      try {
        const content = await window.ipcBridge.call('readFile', '/path/to/file.txt')
        this.displayFileContent(content)
      } catch (error) {
        this.showError('Failed to read file: ' + error.message)
      }
    })

    // 文件上传
    const fileInput = document.getElementById('fileInput') as HTMLInputElement
    fileInput?.addEventListener('change', async (event) => {
      const file = (event.target as HTMLInputElement).files?.[0]
      if (file) {
        try {
          const fileId = await window.ipcBridge.uploadFile(file, {
            onProgress: (progress: number) => {
              this.updateUploadProgress(progress)
            }
          })
          this.showSuccess(`File uploaded successfully: ${fileId}`)
        } catch (error) {
          this.showError('Failed to upload file: ' + error.message)
        }
      }
    })

    // 发布事件按钮
    const publishBtn = document.getElementById('publishBtn')
    publishBtn?.addEventListener('click', () => {
      window.ipcBridge.publish('userAction', {
        action: 'buttonClick',
        timestamp: Date.now(),
        userId: 'user123'
      })
    })
  }

  private updateSystemStatus(status: any) {
    const statusElement = document.getElementById('systemStatus')
    if (statusElement) {
      statusElement.innerHTML = `
        <h3>System Status</h3>
        <p>Uptime: ${Math.floor(status.uptime / 60)} minutes</p>
        <p>Memory Usage: ${Math.round(status.memory.heapUsed / 1024 / 1024)} MB</p>
        <p>Last Update: ${new Date(status.timestamp).toLocaleTimeString()}</p>
      `
    }
  }

  private displaySystemInfo(info: any) {
    const infoElement = document.getElementById('systemInfo')
    if (infoElement) {
      infoElement.innerHTML = `
        <h3>System Information</h3>
        <p>Platform: ${info.platform}</p>
        <p>Architecture: ${info.arch}</p>
        <p>CPU Cores: ${info.cpus}</p>
        <p>Total Memory: ${Math.round(info.memory / 1024 / 1024 / 1024)} GB</p>
      `
    }
  }

  private displayDatabaseResults(results: any) {
    const resultsElement = document.getElementById('databaseResults')
    if (resultsElement) {
      resultsElement.innerHTML = `
        <h3>Database Results</h3>
        <p>Query: ${results.query}</p>
        <p>Results: ${JSON.stringify(results.results, null, 2)}</p>
      `
    }
  }

  private displayFileContent(content: string) {
    const contentElement = document.getElementById('fileContent')
    if (contentElement) {
      contentElement.innerHTML = `
        <h3>File Content</h3>
        <pre>${content}</pre>
      `
    }
  }

  private updateUploadProgress(progress: number) {
    const progressElement = document.getElementById('uploadProgress')
    if (progressElement) {
      progressElement.innerHTML = `Upload Progress: ${Math.round(progress)}%`
    }
  }

  private showSuccess(message: string) {
    this.showMessage(message, 'success')
  }

  private showError(message: string) {
    this.showMessage(message, 'error')
  }

  private showMessage(message: string, type: 'success' | 'error') {
    const messageElement = document.getElementById('messages')
    if (messageElement) {
      const div = document.createElement('div')
      div.className = `message ${type}`
      div.textContent = message
      messageElement.appendChild(div)

      // 自动移除消息
      setTimeout(() => {
        div.remove()
      }, 5000)
    }
  }

  dispose() {
    // 取消所有订阅
    this.subscriptions.forEach(id => {
      window.ipcBridge.unsubscribe?.(id)
    })
  }
}

// 启动渲染进程应用
const app = new RendererApp()
app.initialize()

// 清理资源
window.addEventListener('beforeunload', () => {
  app.dispose()
})

// ============= HTML 示例 (index.html) =============
/*
<!DOCTYPE html>
<html>
<head>
    <title>IPC Communication Example</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .message { padding: 10px; margin: 5px 0; border-radius: 3px; }
        .message.success { background-color: #d4edda; color: #155724; }
        .message.error { background-color: #f8d7da; color: #721c24; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        input[type="file"] { margin: 10px 0; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Electron IPC Communication Example</h1>

        <div class="section">
            <h2>System Information</h2>
            <div id="systemInfo">Loading...</div>
        </div>

        <div class="section">
            <h2>System Status (Real-time)</h2>
            <div id="systemStatus">Waiting for updates...</div>
        </div>

        <div class="section">
            <h2>File Operations</h2>
            <button id="readFileBtn">Read File</button>
            <div id="fileContent"></div>
        </div>

        <div class="section">
            <h2>File Upload</h2>
            <input type="file" id="fileInput" />
            <div id="uploadProgress"></div>
        </div>

        <div class="section">
            <h2>Database Results</h2>
            <div id="databaseResults">Loading...</div>
        </div>

        <div class="section">
            <h2>Event Publishing</h2>
            <button id="publishBtn">Publish User Action</button>
        </div>

        <div class="section">
            <h2>Messages</h2>
            <div id="messages"></div>
        </div>
    </div>

    <script src="renderer.js"></script>
</body>
</html>
*/
