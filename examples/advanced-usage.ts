/**
 * 高级使用示例
 * 展示类型安全、装饰器、流式传输等高级功能
 */

import {
	createIPCServer,
	createIPCClient,
	createTypedRPCClient,
	createTypedEventClient,
	RPCMethod,
	EventListener,
	autoRegisterMethods,
	withRetry,
	withPerformanceMonitoring,
	withErrorHandling,
	RPCMethodMap,
	EventMap
} from '../src/core/ipc'

// ============= 类型定义 =============

// 定义 RPC 方法的类型
interface AppRPCMethods extends RPCMethodMap {
	'user.create': {
		params: [{ name: string; email: string; age: number }]
		result: { id: string; name: string; email: string; createdAt: string }
	}
	'user.get': {
		params: [string] // userId
		result: { id: string; name: string; email: string; age: number } | null
	}
	'user.list': {
		params: [{ page?: number; limit?: number; filter?: string }]
		result: { users: any[]; total: number; page: number }
	}
	'file.process': {
		params: [string, { format: string; quality?: number }]
		result: { processedPath: string; size: number; duration: number }
	}
	'analytics.track': {
		params: [string, Record<string, any>]
		result: { tracked: boolean; eventId: string }
	}
}

// 定义事件的类型
interface AppEvents extends EventMap {
	'user.created': { id: string; name: string; timestamp: number }
	'user.updated': { id: string; changes: Record<string, any>; timestamp: number }
	'user.deleted': { id: string; timestamp: number }
	'file.uploaded': { fileId: string; fileName: string; size: number }
	'file.processed': { fileId: string; result: any }
	'system.alert': { level: 'info' | 'warning' | 'error'; message: string; timestamp: number }
}

// ============= 服务端实现 (主进程) =============

class UserService {
	private users = new Map<string, any>()

	@RPCMethod({ timeout: 5000, description: 'Create a new user' })
	@withRetry(3, 1000)
	@withPerformanceMonitoring('UserService.createUser')
	@withErrorHandling()
	async createUser(userData: { name: string; email: string; age: number }) {
		// 验证输入
		if (!userData.name || !userData.email) {
			throw new Error('Name and email are required')
		}

		const user = {
			id: `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
			...userData,
			createdAt: new Date().toISOString()
		}

		this.users.set(user.id, user)

		// 发布事件
		this.publishEvent('user.created', {
			id: user.id,
			name: user.name,
			timestamp: Date.now()
		})

		return user
	}

	@RPCMethod({ timeout: 3000, description: 'Get user by ID' })
	@withPerformanceMonitoring('UserService.getUser')
	async getUser(userId: string) {
		return this.users.get(userId) || null
	}

	@RPCMethod({ timeout: 10000, description: 'List users with pagination' })
	@withPerformanceMonitoring('UserService.listUsers')
	async listUsers(options: { page?: number; limit?: number; filter?: string } = {}) {
		const { page = 1, limit = 10, filter } = options
		let users = Array.from(this.users.values())

		// 应用过滤器
		if (filter) {
			users = users.filter(
				user =>
					user.name.toLowerCase().includes(filter.toLowerCase()) ||
					user.email.toLowerCase().includes(filter.toLowerCase())
			)
		}

		// 分页
		const start = (page - 1) * limit
		const paginatedUsers = users.slice(start, start + limit)

		return {
			users: paginatedUsers,
			total: users.length,
			page
		}
	}

	@EventListener('user.updated')
	onUserUpdated(data: { id: string; changes: Record<string, any>; timestamp: number }) {
		console.log(`User ${data.id} was updated:`, data.changes)

		// 更新本地数据
		const user = this.users.get(data.id)
		if (user) {
			Object.assign(user, data.changes)
		}
	}

	private publishEvent(event: string, data: any) {
		// 这里应该通过 IPC 服务端发布事件
		// 在实际实现中，需要注入 IPC 服务端实例
	}
}

class FileService {
	@RPCMethod({ timeout: 60000, description: 'Process uploaded file' })
	@withRetry(2, 2000)
	@withPerformanceMonitoring('FileService.processFile')
	async processFile(filePath: string, options: { format: string; quality?: number }) {
		const startTime = Date.now()

		// 模拟文件处理
		await new Promise(resolve => setTimeout(resolve, 2000))

		const result = {
			processedPath: filePath.replace(/\.[^.]+$/, `.processed.${options.format}`),
			size: Math.floor(Math.random() * 1000000),
			duration: Date.now() - startTime
		}

		// 发布处理完成事件
		this.publishEvent('file.processed', {
			fileId: filePath,
			result
		})

		return result
	}

	@EventListener('file.uploaded')
	onFileUploaded(data: { fileId: string; fileName: string; size: number }) {
		console.log(`File uploaded: ${data.fileName} (${data.size} bytes)`)

		// 自动开始处理
		this.processFile(data.fileId, { format: 'webp', quality: 80 }).catch(error =>
			console.error('Auto-processing failed:', error)
		)
	}

	private publishEvent(event: string, data: any) {
		// 实际实现中需要注入 IPC 服务端实例
	}
}

// 主进程服务端设置
class MainProcessServer {
	private ipcServer: any
	private userService: UserService
	private fileService: FileService

	constructor() {
		this.ipcServer = createIPCServer({
			processType: 'main',
			enableLogging: true,
			enableMetrics: true,
			defaultTimeout: 30000
		})

		this.userService = new UserService()
		this.fileService = new FileService()

		this.setupServices()
		this.setupStreamHandlers()
	}

	private setupServices() {
		// 自动注册装饰器标记的方法
		autoRegisterMethods(this.userService, this.ipcServer)
		autoRegisterMethods(this.fileService, this.ipcServer)

		// 手动注册分析服务
		this.ipcServer.registerMethod(
			'analytics.track',
			async (event: string, properties: Record<string, any>) => {
				// 模拟分析跟踪
				console.log(`Analytics: ${event}`, properties)

				return {
					tracked: true,
					eventId: `event-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
				}
			}
		)
	}

	private setupStreamHandlers() {
		// 设置日志流
		this.ipcServer.createStream(
			{
				streamId: 'system-logs',
				bufferSize: 100,
				timeout: 0 // 永不超时
			},
			(chunk: any, isEnd: boolean) => {
				if (!isEnd) {
					console.log('Log stream chunk:', chunk)
				}
			}
		)

		// 定期发送系统状态
		setInterval(() => {
			this.ipcServer.sendStreamChunk('system-logs', {
				timestamp: Date.now(),
				level: 'info',
				message: 'System heartbeat',
				memory: process.memoryUsage(),
				uptime: process.uptime()
			})
		}, 5000)
	}

	dispose() {
		this.ipcServer?.dispose()
	}
}

// ============= 客户端实现 (渲染进程) =============

class RendererClient {
	private ipcClient: any
	private typedRPC: any
	private typedEvents: any
	private subscriptions: string[] = []

	constructor() {
		this.ipcClient = createIPCClient({
			processType: 'renderer',
			enableLogging: true,
			enableMetrics: true
		})

		// 创建类型安全的客户端
		this.typedRPC = createTypedRPCClient<AppRPCMethods>(this.ipcClient)
		this.typedEvents = createTypedEventClient<AppEvents>(this.ipcClient)

		this.setupEventListeners()
	}

	private setupEventListeners() {
		// 监听用户事件
		const userCreatedSub = this.typedEvents.subscribe('user.created', data => {
			console.log('User created:', data)
			this.updateUserList()
		})
		this.subscriptions.push(userCreatedSub)

		// 监听文件事件
		const fileProcessedSub = this.typedEvents.subscribe('file.processed', data => {
			console.log('File processed:', data)
			this.showNotification(`File processed: ${data.fileId}`)
		})
		this.subscriptions.push(fileProcessedSub)

		// 监听系统警报
		const alertSub = this.typedEvents.subscribe('system.alert', data => {
			this.showAlert(data.level, data.message)
		})
		this.subscriptions.push(alertSub)

		// 监听日志流
		const logStreamSub = this.ipcClient.createStream(
			{
				streamId: 'system-logs'
			},
			(chunk: any, isEnd: boolean) => {
				if (!isEnd) {
					this.appendToLogView(chunk)
				}
			}
		)
	}

	// 用户管理方法
	async createUser(userData: { name: string; email: string; age: number }) {
		try {
			const user = await this.typedRPC.call('user.create', [userData])
			console.log('Created user:', user)

			// 跟踪分析事件
			await this.typedRPC.call('analytics.track', [
				'user_created',
				{
					userId: user.id,
					source: 'web_form'
				}
			])

			return user
		} catch (error) {
			console.error('Failed to create user:', error)
			throw error
		}
	}

	async getUserList(options: { page?: number; limit?: number; filter?: string } = {}) {
		try {
			return await this.typedRPC.call('user.list', [options])
		} catch (error) {
			console.error('Failed to get user list:', error)
			throw error
		}
	}

	async processFile(filePath: string, options: { format: string; quality?: number }) {
		try {
			return await this.typedRPC.call('file.process', [filePath, options])
		} catch (error) {
			console.error('Failed to process file:', error)
			throw error
		}
	}

	// 文件上传（带进度）
	async uploadFileWithProgress(file: File): Promise<string> {
		return new Promise((resolve, reject) => {
			let progress = 0

			const fileId = this.ipcClient.uploadFile(file, {
				chunkSize: 64 * 1024,
				onProgress: (p: number) => {
					progress = p
					this.updateUploadProgress(progress)
				}
			})

			fileId
				.then((id: string) => {
					// 发布文件上传事件
					this.typedEvents.publish('file.uploaded', {
						fileId: id,
						fileName: file.name,
						size: file.size
					})
					resolve(id)
				})
				.catch(reject)
		})
	}

	// UI 更新方法
	private updateUserList() {
		this.getUserList().then(result => {
			// 更新用户列表 UI
			console.log('Updated user list:', result)
		})
	}

	private showNotification(message: string) {
		// 显示通知
		console.log('Notification:', message)
	}

	private showAlert(level: string, message: string) {
		// 显示警报
		console.log(`Alert [${level}]:`, message)
	}

	private updateUploadProgress(progress: number) {
		// 更新上传进度 UI
		console.log(`Upload progress: ${progress}%`)
	}

	private appendToLogView(logEntry: any) {
		// 添加到日志视图
		console.log('Log:', logEntry)
	}

	dispose() {
		// 取消所有订阅
		this.subscriptions.forEach(id => {
			this.ipcClient.unsubscribe(id)
		})

		this.ipcClient.dispose()
	}
}

// 使用示例
const client = new RendererClient()

// 创建用户
client
	.createUser({
		name: 'John Doe',
		email: '<EMAIL>',
		age: 30
	})
	.then(user => {
		console.log('User created successfully:', user)
	})

// 获取用户列表
client.getUserList({ page: 1, limit: 10, filter: 'john' }).then(result => {
	console.log('User list:', result)
})

export { MainProcessServer, RendererClient }
