<script setup lang="ts">
import Versions from './components/Versions.vue'
const ipcRenderer = window.electron.ipcRenderer
const ipcHandle = (): void => ipcRenderer.send('ping')
const handleClick = () => {
	ipcRenderer.send('versionCheck')
}
</script>

<template>
	<div class="actions">
		<a target="_blank" rel="noreferrer" @click="ipcHandle">Send IPC</a>
		<a target="_blank" rel="noreferrer" @click="handleClick">Check version</a>
	</div>
	<Versions />
</template>
