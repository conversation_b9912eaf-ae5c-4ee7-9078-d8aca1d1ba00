import path from 'path'
import { app } from 'electron'
import { DEBUG } from '../../env'
const relativePath = 'sdks/trtc/liteav/index'
export function getTRTCSdkPath(): string {
	if (DEBUG) {
		return path.resolve(process.cwd(), relativePath)
	} else {
		return path.resolve(app.getAppPath(), relativePath)
	}
}
export function getTRTCLogPath(): string {
	return path.resolve(app.getPath('userData'), 'trtc')
}
