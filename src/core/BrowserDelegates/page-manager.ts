import * as allBrowserDelegates from '../BrowserDelegates/index'

import WindowFactory, { getActiveWindow, setActiveWindow } from '../WindowFactory'
import { screen } from 'electron'

export async function openWindow({ pack, data }: never): Promise<void> {
	const screenSize = screen.getPrimaryDisplay().size
	const activeWindow = getActiveWindow()!
	const windowFactory = new WindowFactory(screenSize, __dirname)
	if (activeWindow && !activeWindow.isDestroyed()) {
		activeWindow.hide()
	}
	const { winEventer, setDelegate } = windowFactory.open({
		pack,
		data,
		unique: false,
		delegate: { ...allBrowserDelegates }
	})
	winEventer.on('closed', () => {
		console.log('DEBUG_LOG:subWindow closed')
		if (activeWindow && !activeWindow.isDestroyed()) {
			activeWindow.show()
			setActiveWindow(activeWindow)
			setDelegate()
		}
	})
	winEventer.on('loaded', (info) => {
		console.log('DEBUG_LOG:subWindow loaded', info)
	})
	winEventer.on('ipc-message', (info = []) => {
		console.log('DEBUG_LOG:subWindow message', info)
	})
}
export function closeWindow() {
	const activeWindow = getActiveWindow()!
	if (!activeWindow.isDestroyed()) {
		activeWindow.close()
	}
}
export function moveWindowToLeft() {
	const activeWindow = getActiveWindow()!
	if (activeWindow && !activeWindow.isDestroyed()) {
		activeWindow.setPosition(0, 0)
	}
}
export function moveWindowToRight() {
	const activeWindow = getActiveWindow()!
	if (activeWindow && !activeWindow.isDestroyed()) {
		const screenSize = screen.getPrimaryDisplay().size
		activeWindow.setPosition(screenSize.width - activeWindow.getSize()[0], 0)
	}
}
