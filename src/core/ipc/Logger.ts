/**
 * IPC 通信日志器
 * 提供结构化日志记录和调试功能
 */

export enum LogLevel {
	DEBUG = 0,
	INFO = 1,
	WARN = 2,
	ERROR = 3
}

export interface LogEntry {
	timestamp: number
	level: LogLevel
	message: string
	data?: any
	source: string
	processType: 'main' | 'renderer'
}

export class Logger {
	private enabled: boolean
	private logLevel: LogLevel = LogLevel.INFO
	private logs: LogEntry[] = []
	private maxLogs: number = 1000
	private processType: 'main' | 'renderer'

	constructor(enabled: boolean = true) {
		this.enabled = enabled
		this.processType = typeof window !== 'undefined' ? 'renderer' : 'main'
	}

	setLevel(level: LogLevel): void {
		this.logLevel = level
	}

	setMaxLogs(max: number): void {
		this.maxLogs = max
	}

	debug(message: string, data?: any): void {
		this.log(LogLevel.DEBUG, message, data)
	}

	info(message: string, data?: any): void {
		this.log(LogLevel.INFO, message, data)
	}

	warn(message: string, data?: any): void {
		this.log(LogLevel.WARN, message, data)
	}

	error(message: string, data?: any): void {
		this.log(LogLevel.ERROR, message, data)
	}

	private log(level: LogLevel, message: string, data?: any): void {
		if (!this.enabled || level < this.logLevel) {
			return
		}

		const entry: LogEntry = {
			timestamp: Date.now(),
			level,
			message,
			data,
			source: 'IPC',
			processType: this.processType
		}

		// 添加到内存日志
		this.logs.push(entry)
		if (this.logs.length > this.maxLogs) {
			this.logs.shift()
		}

		// 输出到控制台
		this.outputToConsole(entry)
	}

	private outputToConsole(entry: LogEntry): void {
		const timestamp = new Date(entry.timestamp).toISOString()
		const levelName = LogLevel[entry.level]
		const prefix = `[${timestamp}] [${entry.processType.toUpperCase()}] [${levelName}] [${entry.source}]`

		const message = `${prefix} ${entry.message}`

		switch (entry.level) {
			case LogLevel.DEBUG:
				console.debug(message, entry.data || '')
				break
			case LogLevel.INFO:
				console.info(message, entry.data || '')
				break
			case LogLevel.WARN:
				console.warn(message, entry.data || '')
				break
			case LogLevel.ERROR:
				console.error(message, entry.data || '')
				break
		}
	}

	getLogs(level?: LogLevel): LogEntry[] {
		if (level !== undefined) {
			return this.logs.filter(log => log.level >= level)
		}
		return [...this.logs]
	}

	clearLogs(): void {
		this.logs.length = 0
	}

	exportLogs(): string {
		return JSON.stringify(this.logs, null, 2)
	}
}
