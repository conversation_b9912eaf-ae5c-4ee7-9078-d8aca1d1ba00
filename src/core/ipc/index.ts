/**
 * Electron IPC 通信架构 - 主导出文件
 * 高效、类型安全、可扩展的进程间通信解决方案
 */

// ============= 核心类型导出 =============
export * from './types'

// ============= 核心组件导出 =============
export { CommunicationManager } from './CommunicationManager'
export { Logger, LogLevel } from './Logger'
export { MetricsCollector } from './MetricsCollector'

// ============= 协议适配器导出 =============
export * from './adapters'

// ============= 客户端和服务端 API 导出 =============
export { IPCClient } from './IPCClient'
export { IPCServer } from './IPCServer'
export { exposeBridgeAPI } from './PreloadBridge'

// ============= 便捷工厂函数 =============
import { IPCClient } from './IPCClient'
import { IPCServer } from './IPCServer'
import { CommunicationManagerConfig } from './types'

/**
 * 创建 IPC 客户端 (渲染进程使用)
 */
export function createIPCClient(config?: Partial<CommunicationManagerConfig>): IPCClient {
	return new IPCClient(config)
}

/**
 * 创建 IPC 服务端 (主进程使用)
 */
export function createIPCServer(config?: Partial<CommunicationManagerConfig>): IPCServer {
	return new IPCServer(config)
}

// ============= 类型安全的 API 定义 =============

/**
 * 定义 RPC 方法的类型安全接口
 */
export interface RPCMethodMap {
	[methodName: string]: {
		params: any[]
		result: any
	}
}

/**
 * 定义事件的类型安全接口
 */
export interface EventMap {
	[eventName: string]: any
}

/**
 * 类型安全的 RPC 客户端
 */
export interface TypedRPCClient<T extends RPCMethodMap> {
	call<K extends keyof T>(
		method: K,
		params: T[K]['params'],
		options?: any
	): Promise<T[K]['result']>
}

/**
 * 类型安全的事件客户端
 */
export interface TypedEventClient<T extends EventMap> {
	subscribe<K extends keyof T>(event: K, listener: (data: T[K]) => void, options?: any): string

	publish<K extends keyof T>(event: K, data: T[K], namespace?: string): void
}

/**
 * 创建类型安全的 RPC 客户端
 */
export function createTypedRPCClient<T extends RPCMethodMap>(client: IPCClient): TypedRPCClient<T> {
	return {
		call: (method, params, options) => client.call(method as string, params, options)
	}
}

/**
 * 创建类型安全的事件客户端
 */
export function createTypedEventClient<T extends EventMap>(client: IPCClient): TypedEventClient<T> {
	return {
		subscribe: (event, listener, options) =>
			client.subscribe(event as string, listener, options),
		publish: (event, data, namespace) => client.publish(event as string, data, namespace)
	}
}

// ============= 装饰器支持 =============

/**
 * RPC 方法装饰器
 */
export function RPCMethod(options?: {
	timeout?: number
	retries?: number
	validation?: (params: any[]) => boolean
	description?: string
}) {
	return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
		const originalMethod = descriptor.value

		// 添加元数据
		if (!target._rpcMethods) {
			target._rpcMethods = new Map()
		}

		target._rpcMethods.set(propertyKey, {
			name: propertyKey,
			handler: originalMethod,
			...options
		})

		return descriptor
	}
}

/**
 * 事件监听器装饰器
 */
export function EventListener(
	event: string,
	options?: {
		namespace?: string
		once?: boolean
		priority?: any
	}
) {
	return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
		const originalMethod = descriptor.value

		// 添加元数据
		if (!target._eventListeners) {
			target._eventListeners = new Map()
		}

		target._eventListeners.set(propertyKey, {
			event,
			handler: originalMethod,
			...options
		})

		return descriptor
	}
}

/**
 * 自动注册装饰器标记的方法
 */
export function autoRegisterMethods(instance: any, client: IPCClient | IPCServer): void {
	// 注册 RPC 方法
	if (instance._rpcMethods) {
		for (const [methodName, methodDef] of instance._rpcMethods) {
			client.registerMethod(methodDef.name, methodDef.handler.bind(instance), methodDef)
		}
	}

	// 注册事件监听器
	if (instance._eventListeners) {
		for (const [listenerName, listenerDef] of instance._eventListeners) {
			client.subscribe(listenerDef.event, listenerDef.handler.bind(instance), listenerDef)
		}
	}
}

// ============= 错误处理工具 =============

/**
 * IPC 错误处理中间件
 */
export function withErrorHandling<T extends (...args: any[]) => any>(
	fn: T,
	errorHandler?: (error: any, ...args: Parameters<T>) => void
): T {
	return ((...args: Parameters<T>) => {
		try {
			const result = fn(...args)

			if (result instanceof Promise) {
				return result.catch(error => {
					if (errorHandler) {
						errorHandler(error, ...args)
					}
					throw error
				})
			}

			return result
		} catch (error) {
			if (errorHandler) {
				errorHandler(error, ...args)
			}
			throw error
		}
	}) as T
}

/**
 * 重试装饰器
 */
export function withRetry(maxRetries: number = 3, delay: number = 1000) {
	return function <T extends (...args: any[]) => Promise<any>>(
		target: any,
		propertyKey: string,
		descriptor: PropertyDescriptor
	) {
		const originalMethod = descriptor.value

		descriptor.value = async function (...args: any[]) {
			let lastError: any

			for (let attempt = 0; attempt <= maxRetries; attempt++) {
				try {
					return await originalMethod.apply(this, args)
				} catch (error) {
					lastError = error

					if (attempt < maxRetries) {
						await new Promise(resolve =>
							setTimeout(resolve, delay * Math.pow(2, attempt))
						)
					}
				}
			}

			throw lastError
		}

		return descriptor
	}
}

// ============= 性能监控工具 =============

/**
 * 性能监控装饰器
 */
export function withPerformanceMonitoring(label?: string) {
	return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
		const originalMethod = descriptor.value
		const methodLabel = label || `${target.constructor.name}.${propertyKey}`

		descriptor.value = async function (...args: any[]) {
			const startTime = performance.now()

			try {
				const result = await originalMethod.apply(this, args)
				const endTime = performance.now()

				console.debug(`[Performance] ${methodLabel}: ${endTime - startTime}ms`)
				return result
			} catch (error) {
				const endTime = performance.now()
				console.debug(`[Performance] ${methodLabel} (error): ${endTime - startTime}ms`)
				throw error
			}
		}

		return descriptor
	}
}

// ============= 默认配置 =============

/**
 * 默认配置
 */
export const DEFAULT_CONFIG: Required<CommunicationManagerConfig> = {
	processType: 'renderer',
	enableLogging: true,
	enableMetrics: true,
	defaultTimeout: 30000,
	maxRetries: 3,
	retryDelay: 1000,
	enableCompression: false,
	enableEncryption: false
}

/**
 * 开发环境配置
 */
export const DEV_CONFIG: Partial<CommunicationManagerConfig> = {
	enableLogging: true,
	enableMetrics: true,
	defaultTimeout: 10000
}

/**
 * 生产环境配置
 */
export const PROD_CONFIG: Partial<CommunicationManagerConfig> = {
	enableLogging: false,
	enableMetrics: true,
	defaultTimeout: 30000,
	enableCompression: true
}
