/**
 * 预加载脚本桥接器
 * 在预加载脚本中使用，安全地暴露 IPC 功能给渲染进程
 */

import { contextBridge, ipcRenderer } from 'electron'
import { IPCMessage, MessageContext } from './types'

const IPC_CHANNEL = 'ipc-communication'

export interface IPCBridgeAPI {
	// RPC 方法
	call: <TResult = any>(method: string, params?: any[], options?: any) => Promise<TResult>
	registerMethod: (name: string, handler: (...args: any[]) => any) => void
	unregisterMethod: (name: string) => void

	// 事件方法
	subscribe: <TData = any>(
		event: string,
		listener: (data: TData) => void,
		options?: any
	) => string
	unsubscribe: (subscriptionId: string) => boolean
	publish: <TData = any>(event: string, data?: TData, namespace?: string) => void
	once: <TData = any>(event: string, listener: (data: TData) => void, options?: any) => string

	// 流式传输方法
	createStream: <TData = any>(
		config: any,
		handler: (chunk: TData, isEnd: boolean) => void
	) => string
	sendStreamChunk: (streamId: string, chunk: any, isEnd?: boolean) => void
	endStream: (streamId: string, error?: string) => void

	// 文件传输方法
	uploadFile: (file: File, options?: any) => Promise<string>
	cancelFileTransfer: (fileId: string, reason?: string) => void

	// 工具方法
	getDebugInfo: () => any
	isConnected: () => boolean

	// 事件监听
	on: (event: string, listener: (...args: any[]) => void) => void
	off: (event: string, listener: (...args: any[]) => void) => void
}

class PreloadBridge {
	private messageHandlers = new Map<string, (...args: any[]) => any>()
	private eventListeners = new Map<string, Set<(...args: any[]) => void>>()
	private messageId = 0

	constructor() {
		this.setupIpcListeners()
	}

	/**
	 * 创建桥接 API
	 */
	createBridgeAPI(): IPCBridgeAPI {
		return {
			// RPC 方法
			call: this.call.bind(this),
			registerMethod: this.registerMethod.bind(this),
			unregisterMethod: this.unregisterMethod.bind(this),

			// 事件方法
			subscribe: this.subscribe.bind(this),
			unsubscribe: this.unsubscribe.bind(this),
			publish: this.publish.bind(this),
			once: this.once.bind(this),

			// 流式传输方法
			createStream: this.createStream.bind(this),
			sendStreamChunk: this.sendStreamChunk.bind(this),
			endStream: this.endStream.bind(this),

			// 文件传输方法
			uploadFile: this.uploadFile.bind(this),
			cancelFileTransfer: this.cancelFileTransfer.bind(this),

			// 工具方法
			getDebugInfo: this.getDebugInfo.bind(this),
			isConnected: this.isConnected.bind(this),

			// 事件监听
			on: this.on.bind(this),
			off: this.off.bind(this)
		}
	}

	// ============= RPC 方法 =============

	private async call<TResult = any>(
		method: string,
		params?: any[],
		options?: any
	): Promise<TResult> {
		return new Promise((resolve, reject) => {
			const messageId = this.generateMessageId()
			const timeout = options?.timeout || 30000

			const timeoutHandle = setTimeout(() => {
				reject(new Error(`RPC call timeout: ${method}`))
			}, timeout)

			// 监听响应
			const responseHandler = (event: any, message: IPCMessage) => {
				if (message.id === messageId) {
					clearTimeout(timeoutHandle)
					ipcRenderer.off(IPC_CHANNEL, responseHandler)

					if ('error' in message && message.error) {
						reject(new Error(message.error.message))
					} else {
						resolve('result' in message ? message.result : undefined)
					}
				}
			}

			ipcRenderer.on(IPC_CHANNEL, responseHandler)

			// 发送请求
			const requestMessage: IPCMessage = {
				id: messageId,
				type: 'rpc' as any,
				timestamp: Date.now(),
				priority: 'normal',
				source: 'renderer',
				method,
				params
			} as any

			ipcRenderer.send(IPC_CHANNEL, requestMessage)
		})
	}

	private registerMethod(name: string, handler: (...args: any[]) => any): void {
		this.messageHandlers.set(name, handler)
	}

	private unregisterMethod(name: string): void {
		this.messageHandlers.delete(name)
	}

	// ============= 事件方法 =============

	private subscribe<TData = any>(
		event: string,
		listener: (data: TData) => void,
		options?: any
	): string {
		const subscriptionId = `sub-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

		// 发送订阅请求
		const message: IPCMessage = {
			id: this.generateMessageId(),
			type: 'event' as any,
			timestamp: Date.now(),
			priority: 'normal',
			source: 'renderer',
			event: 'subscribe',
			data: { event, subscriptionId, options }
		} as any

		ipcRenderer.send(IPC_CHANNEL, message)

		// 存储监听器
		if (!this.eventListeners.has(event)) {
			this.eventListeners.set(event, new Set())
		}
		this.eventListeners.get(event)!.add(listener)

		return subscriptionId
	}

	private unsubscribe(subscriptionId: string): boolean {
		// 发送取消订阅请求
		const message: IPCMessage = {
			id: this.generateMessageId(),
			type: 'event' as any,
			timestamp: Date.now(),
			priority: 'normal',
			source: 'renderer',
			event: 'unsubscribe',
			data: { subscriptionId }
		} as any

		ipcRenderer.send(IPC_CHANNEL, message)
		return true
	}

	private publish<TData = any>(event: string, data?: TData, namespace?: string): void {
		const message: IPCMessage = {
			id: this.generateMessageId(),
			type: 'event' as any,
			timestamp: Date.now(),
			priority: 'normal',
			source: 'renderer',
			event,
			data,
			namespace
		} as any

		ipcRenderer.send(IPC_CHANNEL, message)
	}

	private once<TData = any>(
		event: string,
		listener: (data: TData) => void,
		options?: any
	): string {
		return this.subscribe(event, listener, { ...options, once: true })
	}

	// ============= 流式传输方法 =============

	private createStream<TData = any>(
		config: any,
		handler: (chunk: TData, isEnd: boolean) => void
	): string {
		const streamId =
			config.streamId || `stream-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

		// 注册流处理器
		this.messageHandlers.set(`stream-${streamId}`, handler)

		// 发送创建流请求
		const message: IPCMessage = {
			id: this.generateMessageId(),
			type: 'stream' as any,
			timestamp: Date.now(),
			priority: 'normal',
			source: 'renderer',
			streamId,
			chunk: null
		} as any

		ipcRenderer.send(IPC_CHANNEL, message)
		return streamId
	}

	private sendStreamChunk(streamId: string, chunk: any, isEnd?: boolean): void {
		const message: IPCMessage = {
			id: this.generateMessageId(),
			type: 'stream' as any,
			timestamp: Date.now(),
			priority: 'normal',
			source: 'renderer',
			streamId,
			chunk,
			isEnd
		} as any

		ipcRenderer.send(IPC_CHANNEL, message)
	}

	private endStream(streamId: string, error?: string): void {
		const message: IPCMessage = {
			id: this.generateMessageId(),
			type: 'stream' as any,
			timestamp: Date.now(),
			priority: 'normal',
			source: 'renderer',
			streamId,
			isEnd: true,
			error
		} as any

		ipcRenderer.send(IPC_CHANNEL, message)

		// 清理处理器
		this.messageHandlers.delete(`stream-${streamId}`)
	}

	// ============= 文件传输方法 =============

	private async uploadFile(file: File, options?: any): Promise<string> {
		const fileId = `upload-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
		const chunkSize = options?.chunkSize || 64 * 1024

		// 发送开始上传消息
		const startMessage: IPCMessage = {
			id: this.generateMessageId(),
			type: 'file' as any,
			timestamp: Date.now(),
			priority: 'normal',
			source: 'renderer',
			operation: 'upload',
			fileId,
			fileName: file.name,
			fileSize: file.size
		} as any

		ipcRenderer.send(IPC_CHANNEL, startMessage)

		// 分块上传文件
		let offset = 0
		while (offset < file.size) {
			const chunk = file.slice(offset, offset + chunkSize)
			const buffer = await this.fileToArrayBuffer(chunk)

			const chunkMessage: IPCMessage = {
				id: this.generateMessageId(),
				type: 'file' as any,
				timestamp: Date.now(),
				priority: 'normal',
				source: 'renderer',
				operation: 'upload',
				fileId,
				chunk: Array.from(new Uint8Array(buffer))
			} as any

			ipcRenderer.send(IPC_CHANNEL, chunkMessage)
			offset += chunkSize

			// 报告进度
			if (options?.onProgress) {
				options.onProgress((offset / file.size) * 100)
			}
		}

		// 发送完成消息
		const completeMessage: IPCMessage = {
			id: this.generateMessageId(),
			type: 'file' as any,
			timestamp: Date.now(),
			priority: 'normal',
			source: 'renderer',
			operation: 'complete',
			fileId
		} as any

		ipcRenderer.send(IPC_CHANNEL, completeMessage)
		return fileId
	}

	private cancelFileTransfer(fileId: string, reason?: string): void {
		const message: IPCMessage = {
			id: this.generateMessageId(),
			type: 'file' as any,
			timestamp: Date.now(),
			priority: 'normal',
			source: 'renderer',
			operation: 'error',
			fileId,
			error: reason
		} as any

		ipcRenderer.send(IPC_CHANNEL, message)
	}

	// ============= 工具方法 =============

	private getDebugInfo(): any {
		// 返回客户端调试信息
		return {
			registeredMethods: Array.from(this.messageHandlers.keys()),
			eventListeners: Object.fromEntries(
				Array.from(this.eventListeners.entries()).map(([event, listeners]) => [
					event,
					listeners.size
				])
			)
		}
	}

	private isConnected(): boolean {
		return true // 在预加载脚本中总是连接的
	}

	private on(event: string, listener: (...args: any[]) => void): void {
		if (!this.eventListeners.has(event)) {
			this.eventListeners.set(event, new Set())
		}
		this.eventListeners.get(event)!.add(listener)
	}

	private off(event: string, listener: (...args: any[]) => void): void {
		const listeners = this.eventListeners.get(event)
		if (listeners) {
			listeners.delete(listener)
			if (listeners.size === 0) {
				this.eventListeners.delete(event)
			}
		}
	}

	// ============= 私有方法 =============

	private setupIpcListeners(): void {
		ipcRenderer.on(IPC_CHANNEL, (event, message: IPCMessage) => {
			this.handleMessage(message)
		})
	}

	private handleMessage(message: IPCMessage): void {
		// 处理不同类型的消息
		if ('method' in message) {
			// RPC 调用
			const handler = this.messageHandlers.get(message.method as string)
			if (handler) {
				try {
					const result = handler(...((message.params as any[]) || []))
					// 发送响应
					const response: IPCMessage = {
						id: this.generateMessageId(),
						type: 'rpc' as any,
						timestamp: Date.now(),
						priority: 'normal',
						source: 'renderer',
						requestId: message.id,
						result
					} as any
					ipcRenderer.send(IPC_CHANNEL, response)
				} catch (error) {
					// 发送错误响应
					const errorResponse: IPCMessage = {
						id: this.generateMessageId(),
						type: 'rpc' as any,
						timestamp: Date.now(),
						priority: 'normal',
						source: 'renderer',
						requestId: message.id,
						error: {
							code: 'INTERNAL_ERROR',
							message: error.message
						}
					} as any
					ipcRenderer.send(IPC_CHANNEL, errorResponse)
				}
			}
		} else if ('event' in message) {
			// 事件消息
			const listeners = this.eventListeners.get(message.event as string)
			if (listeners) {
				listeners.forEach(listener => {
					try {
						listener(message.data)
					} catch (error) {
						console.error('Event listener error:', error)
					}
				})
			}
		}
	}

	private generateMessageId(): string {
		return `preload-${Date.now()}-${++this.messageId}`
	}

	private async fileToArrayBuffer(file: Blob): Promise<ArrayBuffer> {
		return new Promise((resolve, reject) => {
			const reader = new FileReader()
			reader.onload = () => resolve(reader.result as ArrayBuffer)
			reader.onerror = reject
			reader.readAsArrayBuffer(file)
		})
	}
}

// 创建桥接器实例
const bridge = new PreloadBridge()

// 暴露 API 到渲染进程
export function exposeBridgeAPI(): void {
	if (process.contextIsolated) {
		contextBridge.exposeInMainWorld('ipcBridge', bridge.createBridgeAPI())
	} else {
		// 非隔离上下文的回退方案
		;(global as any).ipcBridge = bridge.createBridgeAPI()
	}
}

// 默认暴露 API
exposeBridgeAPI()
