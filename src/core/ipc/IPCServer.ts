/**
 * IPC 服务端 API (主进程端)
 * 提供类型安全的通信接口
 */

import { EventEmitter } from 'events'
import { BrowserWindow } from 'electron'
import { CommunicationManager } from './CommunicationManager'
import { RPCAdapter } from './adapters/RPCAdapter'
import { EventAdapter } from './adapters/EventAdapter'
import { StreamAdapter } from './adapters/StreamAdapter'
import { FileAdapter } from './adapters/FileAdapter'
import {
	CommunicationManagerConfig,
	RPCCallOptions,
	EventSubscriptionOptions,
	StreamConfig,
	FileTransferConfig,
	MessageContext,
	EventListener,
	StreamHandler,
	MessageHandler,
	ProtocolType,
	IPCMessage
} from './types'

export class IPCServer extends EventEmitter {
	private manager: CommunicationManager
	private rpcAdapter: RPCAdapter
	private eventAdapter: EventAdapter
	private streamAdapter: StreamAdapter
	private fileAdapter: FileAdapter
	private connectedWindows = new Set<BrowserWindow>()

	constructor(config?: Partial<CommunicationManagerConfig>) {
		super()

		const fullConfig: CommunicationManagerConfig = {
			processType: 'main',
			enableLogging: true,
			enableMetrics: true,
			defaultTimeout: 30000,
			maxRetries: 3,
			retryDelay: 1000,
			...config
		}

		this.manager = new CommunicationManager(fullConfig)
		this.rpcAdapter = new RPCAdapter()
		this.eventAdapter = new EventAdapter()
		this.streamAdapter = new StreamAdapter()
		this.fileAdapter = new FileAdapter()

		this.setupAdapters()
		this.setupEventForwarding()
	}

	// ============= 连接管理 =============

	/**
	 * 注册窗口连接
	 */
	registerWindow(window: BrowserWindow): void {
		this.connectedWindows.add(window)

		window.on('closed', () => {
			this.connectedWindows.delete(window)
		})

		this.emit('windowConnected', window)
	}

	/**
	 * 注销窗口连接
	 */
	unregisterWindow(window: BrowserWindow): void {
		this.connectedWindows.delete(window)
		this.emit('windowDisconnected', window)
	}

	/**
	 * 获取已连接的窗口
	 */
	getConnectedWindows(): BrowserWindow[] {
		return Array.from(this.connectedWindows).filter(window => !window.isDestroyed())
	}

	// ============= RPC 方法 =============

	/**
	 * 调用渲染进程方法
	 */
	async call<TResult = any>(
		window: BrowserWindow,
		method: string,
		params?: any[],
		options?: RPCCallOptions
	): Promise<TResult> {
		// 设置发送者上下文
		const originalCall = this.rpcAdapter.call.bind(this.rpcAdapter)
		return originalCall(method, params, { ...options, sender: window })
	}

	/**
	 * 向所有窗口调用方法
	 */
	async callAll<TResult = any>(
		method: string,
		params?: any[],
		options?: RPCCallOptions
	): Promise<TResult[]> {
		const windows = this.getConnectedWindows()
		const promises = windows.map(window => this.call<TResult>(window, method, params, options))

		const results = await Promise.allSettled(promises)
		return results
			.filter(result => result.status === 'fulfilled')
			.map(result => (result as PromiseFulfilledResult<TResult>).value)
	}

	/**
	 * 注册服务端方法
	 */
	registerMethod(
		name: string,
		handler: MessageHandler,
		options?: {
			timeout?: number
			retries?: number
			validation?: (params: any[]) => boolean
			description?: string
		}
	): void {
		this.rpcAdapter.registerMethod({
			name,
			handler,
			...options
		})
	}

	/**
	 * 注销服务端方法
	 */
	unregisterMethod(name: string): void {
		this.rpcAdapter.unregisterMethod(name)
	}

	// ============= 事件方法 =============

	/**
	 * 订阅事件
	 */
	subscribe<TData = any>(
		event: string,
		listener: EventListener<TData>,
		options?: EventSubscriptionOptions
	): string {
		return this.eventAdapter.subscribe(event, listener, options)
	}

	/**
	 * 取消订阅
	 */
	unsubscribe(subscriptionId: string): boolean {
		return this.eventAdapter.unsubscribe(subscriptionId)
	}

	/**
	 * 向指定窗口发布事件
	 */
	publish<TData = any>(
		window: BrowserWindow,
		event: string,
		data?: TData,
		namespace?: string
	): void {
		// TODO: 实现向特定窗口发送事件
		this.eventAdapter.publish(event, data, namespace)
	}

	/**
	 * 向所有窗口广播事件
	 */
	broadcast<TData = any>(event: string, data?: TData, namespace?: string): void {
		const windows = this.getConnectedWindows()
		windows.forEach(window => {
			this.publish(window, event, data, namespace)
		})
	}

	// ============= 流式传输方法 =============

	/**
	 * 创建数据流
	 */
	createStream<TData = any>(config: StreamConfig, handler: StreamHandler<TData>): string {
		return this.streamAdapter.createStream(config, handler)
	}

	/**
	 * 发送流数据块到指定窗口
	 */
	sendStreamChunk(window: BrowserWindow, streamId: string, chunk: any, isEnd?: boolean): void {
		// TODO: 实现向特定窗口发送流数据
		this.streamAdapter.sendChunk(streamId, chunk, isEnd)
	}

	/**
	 * 结束流
	 */
	endStream(streamId: string, error?: string): void {
		this.streamAdapter.endStream(streamId, error)
	}

	// ============= 文件传输方法 =============

	/**
	 * 开始文件下载到渲染进程
	 */
	async downloadFile(
		window: BrowserWindow,
		filePath: string,
		options?: {
			chunkSize?: number
			onProgress?: (progress: number) => void
		}
	): Promise<string> {
		const fs = await import('fs/promises')
		const path = await import('path')

		const fileId = `download-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
		const fileName = path.basename(filePath)

		try {
			const stats = await fs.stat(filePath)
			const fileSize = stats.size
			const chunkSize = options?.chunkSize || 64 * 1024

			const config: FileTransferConfig = {
				fileId,
				fileName,
				fileSize,
				chunkSize
			}

			this.fileAdapter.startUpload(config)

			// 监听进度事件
			if (options?.onProgress) {
				this.fileAdapter.on('uploadProgress', event => {
					if (event.fileId === fileId) {
						options.onProgress!(event.progress.percentage)
					}
				})
			}

			// 分块读取并发送文件
			const fileHandle = await fs.open(filePath, 'r')
			let offset = 0

			try {
				while (offset < fileSize) {
					const buffer = Buffer.alloc(Math.min(chunkSize, fileSize - offset))
					const { bytesRead } = await fileHandle.read(buffer, 0, buffer.length, offset)

					if (bytesRead > 0) {
						const chunk = buffer.slice(0, bytesRead)
						this.fileAdapter.sendFileChunk(fileId, chunk)
						offset += bytesRead
					} else {
						break
					}
				}
			} finally {
				await fileHandle.close()
			}

			this.fileAdapter.completeTransfer(fileId)
			return fileId
		} catch (error) {
			this.fileAdapter.cancelTransfer(fileId, error.message)
			throw error
		}
	}

	/**
	 * 取消文件传输
	 */
	cancelFileTransfer(fileId: string, reason?: string): void {
		this.fileAdapter.cancelTransfer(fileId, reason)
	}

	// ============= 工具方法 =============

	/**
	 * 获取调试信息
	 */
	getDebugInfo() {
		return {
			...this.manager.getDebugInfo(),
			connectedWindows: this.connectedWindows.size
		}
	}

	/**
	 * 获取服务器状态
	 */
	getServerStatus() {
		return {
			isRunning: true,
			connectedWindows: this.connectedWindows.size,
			registeredMethods: this.rpcAdapter.getRegisteredMethods(),
			subscribedEvents: this.eventAdapter.getSubscribedEvents(),
			activeStreams: this.streamAdapter.getAllStreamStates(),
			activeTransfers: this.fileAdapter.getActiveTransfers()
		}
	}

	/**
	 * 清理资源
	 */
	dispose(): void {
		this.connectedWindows.clear()
		this.rpcAdapter.dispose()
		this.eventAdapter.dispose()
		this.streamAdapter.dispose()
		this.fileAdapter.dispose()
		this.manager.dispose()
		this.removeAllListeners()
	}

	// ============= 私有方法 =============

	private setupAdapters(): void {
		this.manager.registerAdapter(this.rpcAdapter)
		this.manager.registerAdapter(this.eventAdapter)
		this.manager.registerAdapter(this.streamAdapter)
		this.manager.registerAdapter(this.fileAdapter)
	}

	private setupEventForwarding(): void {
		// 转发 RPC 调用
		this.rpcAdapter.on('call', message => {
			this.manager.send(message)
		})

		// 转发事件发布
		this.eventAdapter.on('publish', message => {
			this.manager.send(message)
		})

		// 转发流数据
		this.streamAdapter.on('sendChunk', message => {
			this.manager.send(message)
		})

		this.streamAdapter.on('endStream', message => {
			this.manager.send(message)
		})

		// 转发文件传输
		this.fileAdapter.on('sendChunk', message => {
			this.manager.send(message)
		})

		this.fileAdapter.on('completeTransfer', message => {
			this.manager.send(message)
		})

		this.fileAdapter.on('cancelTransfer', message => {
			this.manager.send(message)
		})

		// 转发错误事件
		this.manager.on('error', (error, message) => {
			this.emit('error', error, message)
		})
	}

	/**
	 * 处理来自渲染进程的消息
	 */
	handleMessage(message: IPCMessage, context: MessageContext): void {
		this.manager.handleMessage(message, context)
	}
}
