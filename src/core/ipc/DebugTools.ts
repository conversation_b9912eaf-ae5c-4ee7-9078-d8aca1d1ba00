/**
 * IPC 调试和监控工具
 * 提供实时监控、性能分析和调试功能
 */

import { EventEmitter } from 'events'
import { IPCMessage, MessageMetrics, PerformanceMetrics, DebugInfo } from './types'
import { Logger } from './Logger'
import { MetricsCollector } from './MetricsCollector'

export interface DebugSession {
	id: string
	startTime: number
	endTime?: number
	messages: IPCMessage[]
	errors: any[]
	metrics: MessageMetrics
}

export interface MessageTrace {
	messageId: string
	type: string
	method?: string
	event?: string
	startTime: number
	endTime?: number
	duration?: number
	status: 'pending' | 'success' | 'error' | 'timeout'
	error?: any
	stackTrace?: string[]
}

export class DebugTools extends EventEmitter {
	private isEnabled: boolean = false
	private currentSession?: DebugSession
	private messageTraces = new Map<string, MessageTrace>()
	private performanceMarks = new Map<string, number>()
	private logger: Logger
	private metrics: MetricsCollector
	private maxTraces: number = 1000
	private maxSessions: number = 10
	private sessions: DebugSession[] = []

	constructor(logger: Logger, metrics: MetricsCollector) {
		super()
		this.logger = logger
		this.metrics = metrics
	}

	/**
	 * 启用调试模式
	 */
	enable(): void {
		this.isEnabled = true
		this.startNewSession()
		this.emit('debugEnabled')
	}

	/**
	 * 禁用调试模式
	 */
	disable(): void {
		this.isEnabled = false
		this.endCurrentSession()
		this.emit('debugDisabled')
	}

	/**
	 * 开始新的调试会话
	 */
	startNewSession(): string {
		if (this.currentSession) {
			this.endCurrentSession()
		}

		const sessionId = `debug-session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

		this.currentSession = {
			id: sessionId,
			startTime: Date.now(),
			messages: [],
			errors: [],
			metrics: {
				totalMessages: 0,
				messagesByType: {} as any,
				averageResponseTime: 0,
				errorRate: 0,
				throughput: 0
			}
		}

		this.emit('sessionStarted', sessionId)
		return sessionId
	}

	/**
	 * 结束当前调试会话
	 */
	endCurrentSession(): void {
		if (!this.currentSession) {
			return
		}

		this.currentSession.endTime = Date.now()
		this.currentSession.metrics = this.metrics.getMessageMetrics()

		// 保存会话
		this.sessions.push(this.currentSession)

		// 限制会话数量
		if (this.sessions.length > this.maxSessions) {
			this.sessions.shift()
		}

		this.emit('sessionEnded', this.currentSession.id)
		this.currentSession = undefined
	}

	/**
	 * 跟踪消息
	 */
	traceMessage(message: IPCMessage): void {
		if (!this.isEnabled) {
			return
		}

		const trace: MessageTrace = {
			messageId: message.id,
			type: message.type,
			method: 'method' in message ? (message.method as string) : undefined,
			event: 'event' in message ? (message.event as string) : undefined,
			startTime: Date.now(),
			status: 'pending',
			stackTrace: this.captureStackTrace()
		}

		this.messageTraces.set(message.id, trace)

		// 添加到当前会话
		if (this.currentSession) {
			this.currentSession.messages.push(message)
		}

		// 限制跟踪数量
		if (this.messageTraces.size > this.maxTraces) {
			const oldestId = this.messageTraces.keys().next().value
			this.messageTraces.delete(oldestId)
		}

		this.emit('messageTraced', trace)
	}

	/**
	 * 标记消息完成
	 */
	markMessageComplete(messageId: string, success: boolean, error?: any): void {
		if (!this.isEnabled) {
			return
		}

		const trace = this.messageTraces.get(messageId)
		if (!trace) {
			return
		}

		trace.endTime = Date.now()
		trace.duration = trace.endTime - trace.startTime
		trace.status = success ? 'success' : 'error'

		if (error) {
			trace.error = error

			// 添加到当前会话的错误列表
			if (this.currentSession) {
				this.currentSession.errors.push({
					messageId,
					error,
					timestamp: Date.now()
				})
			}
		}

		this.emit('messageCompleted', trace)
	}

	/**
	 * 标记消息超时
	 */
	markMessageTimeout(messageId: string): void {
		if (!this.isEnabled) {
			return
		}

		const trace = this.messageTraces.get(messageId)
		if (!trace) {
			return
		}

		trace.endTime = Date.now()
		trace.duration = trace.endTime - trace.startTime
		trace.status = 'timeout'

		this.emit('messageTimeout', trace)
	}

	/**
	 * 添加性能标记
	 */
	mark(name: string): void {
		if (!this.isEnabled) {
			return
		}

		this.performanceMarks.set(name, performance.now())
		this.emit('performanceMark', { name, timestamp: performance.now() })
	}

	/**
	 * 测量性能
	 */
	measure(name: string, startMark: string, endMark?: string): number | undefined {
		if (!this.isEnabled) {
			return undefined
		}

		const startTime = this.performanceMarks.get(startMark)
		if (!startTime) {
			return undefined
		}

		const endTime = endMark ? this.performanceMarks.get(endMark) : performance.now()

		if (!endTime) {
			return undefined
		}

		const duration = endTime - startTime
		this.emit('performanceMeasure', { name, startMark, endMark, duration })

		return duration
	}

	/**
	 * 获取消息跟踪信息
	 */
	getMessageTrace(messageId: string): MessageTrace | undefined {
		return this.messageTraces.get(messageId)
	}

	/**
	 * 获取所有消息跟踪
	 */
	getAllMessageTraces(): MessageTrace[] {
		return Array.from(this.messageTraces.values())
	}

	/**
	 * 获取性能统计
	 */
	getPerformanceStats(): {
		averageResponseTime: number
		slowestMessages: MessageTrace[]
		fastestMessages: MessageTrace[]
		errorRate: number
		timeoutRate: number
	} {
		const traces = this.getAllMessageTraces()
		const completedTraces = traces.filter(t => t.duration !== undefined)

		if (completedTraces.length === 0) {
			return {
				averageResponseTime: 0,
				slowestMessages: [],
				fastestMessages: [],
				errorRate: 0,
				timeoutRate: 0
			}
		}

		const durations = completedTraces.map(t => t.duration!)
		const averageResponseTime = durations.reduce((sum, d) => sum + d, 0) / durations.length

		const sortedByDuration = [...completedTraces].sort(
			(a, b) => (b.duration || 0) - (a.duration || 0)
		)
		const slowestMessages = sortedByDuration.slice(0, 10)
		const fastestMessages = sortedByDuration.slice(-10).reverse()

		const errorCount = traces.filter(t => t.status === 'error').length
		const timeoutCount = traces.filter(t => t.status === 'timeout').length

		return {
			averageResponseTime,
			slowestMessages,
			fastestMessages,
			errorRate: errorCount / traces.length,
			timeoutRate: timeoutCount / traces.length
		}
	}

	/**
	 * 获取调试会话列表
	 */
	getSessions(): DebugSession[] {
		return [...this.sessions]
	}

	/**
	 * 获取当前会话
	 */
	getCurrentSession(): DebugSession | undefined {
		return this.currentSession
	}

	/**
	 * 导出调试数据
	 */
	exportDebugData(): {
		sessions: DebugSession[]
		traces: MessageTrace[]
		performanceStats: any
		logs: any[]
	} {
		return {
			sessions: this.getSessions(),
			traces: this.getAllMessageTraces(),
			performanceStats: this.getPerformanceStats(),
			logs: this.logger.getLogs()
		}
	}

	/**
	 * 清理调试数据
	 */
	clear(): void {
		this.messageTraces.clear()
		this.performanceMarks.clear()
		this.sessions.length = 0
		this.currentSession = undefined
		this.emit('debugDataCleared')
	}

	/**
	 * 生成调试报告
	 */
	generateReport(): string {
		const stats = this.getPerformanceStats()
		const sessions = this.getSessions()
		const currentSession = this.getCurrentSession()

		const report = [
			'=== IPC Debug Report ===',
			`Generated at: ${new Date().toISOString()}`,
			'',
			'=== Performance Statistics ===',
			`Average Response Time: ${stats.averageResponseTime.toFixed(2)}ms`,
			`Error Rate: ${(stats.errorRate * 100).toFixed(2)}%`,
			`Timeout Rate: ${(stats.timeoutRate * 100).toFixed(2)}%`,
			'',
			'=== Slowest Messages ===',
			...stats.slowestMessages
				.slice(0, 5)
				.map(
					trace =>
						`${trace.messageId}: ${trace.duration}ms (${trace.type}${trace.method ? `:${trace.method}` : ''})`
				),
			'',
			'=== Sessions ===',
			`Total Sessions: ${sessions.length}`,
			`Current Session: ${currentSession ? currentSession.id : 'None'}`,
			'',
			'=== Message Traces ===',
			`Total Traces: ${this.messageTraces.size}`,
			`Pending: ${this.getAllMessageTraces().filter(t => t.status === 'pending').length}`,
			`Success: ${this.getAllMessageTraces().filter(t => t.status === 'success').length}`,
			`Error: ${this.getAllMessageTraces().filter(t => t.status === 'error').length}`,
			`Timeout: ${this.getAllMessageTraces().filter(t => t.status === 'timeout').length}`
		]

		return report.join('\n')
	}

	// ============= 私有方法 =============

	private captureStackTrace(): string[] {
		const stack = new Error().stack
		if (!stack) {
			return []
		}

		return stack
			.split('\n')
			.slice(3) // 跳过 Error 和当前方法的堆栈
			.map(line => line.trim())
			.filter(line => line.length > 0)
	}

	/**
	 * 设置最大跟踪数量
	 */
	setMaxTraces(max: number): void {
		this.maxTraces = max
	}

	/**
	 * 设置最大会话数量
	 */
	setMaxSessions(max: number): void {
		this.maxSessions = max
	}
}

/**
 * 调试装饰器
 */
export function withDebugTrace(debugTools: DebugTools) {
	return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
		const originalMethod = descriptor.value

		descriptor.value = async function (...args: any[]) {
			const markName = `${target.constructor.name}.${propertyKey}`
			debugTools.mark(`${markName}-start`)

			try {
				const result = await originalMethod.apply(this, args)
				debugTools.mark(`${markName}-end`)
				debugTools.measure(markName, `${markName}-start`, `${markName}-end`)
				return result
			} catch (error) {
				debugTools.mark(`${markName}-error`)
				debugTools.measure(`${markName}-error`, `${markName}-start`, `${markName}-error`)
				throw error
			}
		}

		return descriptor
	}
}
