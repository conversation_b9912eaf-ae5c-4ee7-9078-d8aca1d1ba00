/**
 * IPC 错误处理和重试机制
 * 提供完善的错误处理、超时控制和自动重试功能
 */

import { EventEmitter } from 'events'
import { IPCError, IPCErrorType, IPCMessage } from './types'

export interface RetryConfig {
  maxRetries: number
  baseDelay: number
  maxDelay: number
  backoffFactor: number
  jitter: boolean
}

export interface CircuitBreakerConfig {
  failureThreshold: number
  resetTimeout: number
  monitoringPeriod: number
}

export interface ErrorHandlerConfig {
  enableRetry: boolean
  enableCircuitBreaker: boolean
  enableRateLimiting: boolean
  retryConfig: RetryConfig
  circuitBreakerConfig: CircuitBreakerConfig
  rateLimitConfig: {
    maxRequests: number
    windowMs: number
  }
}

export enum CircuitBreakerState {
  CLOSED = 'closed',
  OPEN = 'open',
  HALF_OPEN = 'half_open'
}

export class ErrorHandler extends EventEmitter {
  private config: ErrorHandlerConfig
  private retryAttempts = new Map<string, number>()
  private circuitBreakerState = CircuitBreakerState.CLOSED
  private circuitBreakerFailures = 0
  private circuitBreakerLastFailure = 0
  private rateLimitRequests = new Map<string, number[]>()

  constructor(config: Partial<ErrorHandlerConfig> = {}) {
    super()
    
    this.config = {
      enableRetry: true,
      enableCircuitBreaker: true,
      enableRateLimiting: true,
      retryConfig: {
        maxRetries: 3,
        baseDelay: 1000,
        maxDelay: 30000,
        backoffFactor: 2,
        jitter: true
      },
      circuitBreakerConfig: {
        failureThreshold: 5,
        resetTimeout: 60000,
        monitoringPeriod: 60000
      },
      rateLimitConfig: {
        maxRequests: 100,
        windowMs: 60000
      },
      ...config
    }

    this.setupCircuitBreakerMonitoring()
  }

  /**
   * 处理错误并决定是否重试
   */
  async handleError(
    error: any,
    context: {
      messageId: string
      method?: string
      attempt: number
      originalMessage: IPCMessage
    }
  ): Promise<{ shouldRetry: boolean; delay?: number; transformedError?: Error }> {
    const { messageId, method, attempt, originalMessage } = context

    // 记录错误
    this.emit('error', {
      error,
      messageId,
      method,
      attempt,
      timestamp: Date.now()
    })

    // 检查熔断器状态
    if (this.config.enableCircuitBreaker && this.circuitBreakerState === CircuitBreakerState.OPEN) {
      return {
        shouldRetry: false,
        transformedError: new IPCError(
          IPCErrorType.NETWORK_ERROR,
          'Circuit breaker is open',
          { originalError: error }
        )
      }
    }

    // 检查是否应该重试
    const shouldRetry = this.shouldRetry(error, attempt)
    
    if (shouldRetry && this.config.enableRetry) {
      const delay = this.calculateRetryDelay(attempt)
      
      // 更新重试计数
      this.retryAttempts.set(messageId, attempt + 1)
      
      this.emit('retry', {
        messageId,
        method,
        attempt: attempt + 1,
        delay,
        error
      })

      return { shouldRetry: true, delay }
    }

    // 更新熔断器状态
    if (this.config.enableCircuitBreaker) {
      this.recordFailure()
    }

    // 清理重试计数
    this.retryAttempts.delete(messageId)

    return {
      shouldRetry: false,
      transformedError: this.transformError(error)
    }
  }

  /**
   * 记录成功调用
   */
  recordSuccess(messageId: string): void {
    // 清理重试计数
    this.retryAttempts.delete(messageId)

    // 重置熔断器（如果处于半开状态）
    if (this.config.enableCircuitBreaker && this.circuitBreakerState === CircuitBreakerState.HALF_OPEN) {
      this.circuitBreakerState = CircuitBreakerState.CLOSED
      this.circuitBreakerFailures = 0
      this.emit('circuitBreakerClosed')
    }
  }

  /**
   * 检查速率限制
   */
  checkRateLimit(identifier: string): boolean {
    if (!this.config.enableRateLimiting) {
      return true
    }

    const now = Date.now()
    const windowStart = now - this.config.rateLimitConfig.windowMs
    
    // 获取或创建请求记录
    let requests = this.rateLimitRequests.get(identifier) || []
    
    // 清理过期的请求记录
    requests = requests.filter(timestamp => timestamp > windowStart)
    
    // 检查是否超过限制
    if (requests.length >= this.config.rateLimitConfig.maxRequests) {
      this.emit('rateLimitExceeded', { identifier, requests: requests.length })
      return false
    }

    // 记录新请求
    requests.push(now)
    this.rateLimitRequests.set(identifier, requests)
    
    return true
  }

  /**
   * 获取熔断器状态
   */
  getCircuitBreakerState(): CircuitBreakerState {
    return this.circuitBreakerState
  }

  /**
   * 获取错误统计
   */
  getErrorStats(): {
    totalRetries: number
    circuitBreakerState: CircuitBreakerState
    circuitBreakerFailures: number
    activeRetries: number
  } {
    return {
      totalRetries: Array.from(this.retryAttempts.values()).reduce((sum, count) => sum + count, 0),
      circuitBreakerState: this.circuitBreakerState,
      circuitBreakerFailures: this.circuitBreakerFailures,
      activeRetries: this.retryAttempts.size
    }
  }

  /**
   * 重置错误处理器状态
   */
  reset(): void {
    this.retryAttempts.clear()
    this.circuitBreakerState = CircuitBreakerState.CLOSED
    this.circuitBreakerFailures = 0
    this.circuitBreakerLastFailure = 0
    this.rateLimitRequests.clear()
  }

  // ============= 私有方法 =============

  private shouldRetry(error: any, attempt: number): boolean {
    // 检查重试次数
    if (attempt >= this.config.retryConfig.maxRetries) {
      return false
    }

    // 检查错误类型
    if (error instanceof IPCError) {
      switch (error.type) {
        case IPCErrorType.TIMEOUT:
        case IPCErrorType.NETWORK_ERROR:
          return true
        case IPCErrorType.METHOD_NOT_FOUND:
        case IPCErrorType.INVALID_PARAMS:
        case IPCErrorType.PERMISSION_DENIED:
          return false
        default:
          return true
      }
    }

    // 对于其他错误，默认重试
    return true
  }

  private calculateRetryDelay(attempt: number): number {
    const { baseDelay, maxDelay, backoffFactor, jitter } = this.config.retryConfig
    
    // 指数退避
    let delay = Math.min(baseDelay * Math.pow(backoffFactor, attempt), maxDelay)
    
    // 添加抖动
    if (jitter) {
      delay = delay * (0.5 + Math.random() * 0.5)
    }
    
    return Math.floor(delay)
  }

  private transformError(error: any): Error {
    if (error instanceof IPCError) {
      return error
    }

    if (error instanceof Error) {
      return new IPCError(
        IPCErrorType.INTERNAL_ERROR,
        error.message,
        { originalError: error },
        error.name
      )
    }

    return new IPCError(
      IPCErrorType.INTERNAL_ERROR,
      'Unknown error occurred',
      { originalError: error }
    )
  }

  private recordFailure(): void {
    this.circuitBreakerFailures++
    this.circuitBreakerLastFailure = Date.now()

    if (this.circuitBreakerFailures >= this.config.circuitBreakerConfig.failureThreshold) {
      this.circuitBreakerState = CircuitBreakerState.OPEN
      this.emit('circuitBreakerOpened', {
        failures: this.circuitBreakerFailures,
        threshold: this.config.circuitBreakerConfig.failureThreshold
      })
    }
  }

  private setupCircuitBreakerMonitoring(): void {
    if (!this.config.enableCircuitBreaker) {
      return
    }

    setInterval(() => {
      const now = Date.now()
      const { resetTimeout, monitoringPeriod } = this.config.circuitBreakerConfig

      // 检查是否应该从开启状态转为半开状态
      if (
        this.circuitBreakerState === CircuitBreakerState.OPEN &&
        now - this.circuitBreakerLastFailure >= resetTimeout
      ) {
        this.circuitBreakerState = CircuitBreakerState.HALF_OPEN
        this.emit('circuitBreakerHalfOpen')
      }

      // 清理过期的速率限制记录
      if (this.config.enableRateLimiting) {
        const windowStart = now - this.config.rateLimitConfig.windowMs
        for (const [identifier, requests] of this.rateLimitRequests) {
          const validRequests = requests.filter(timestamp => timestamp > windowStart)
          if (validRequests.length === 0) {
            this.rateLimitRequests.delete(identifier)
          } else {
            this.rateLimitRequests.set(identifier, validRequests)
          }
        }
      }
    }, monitoringPeriod)
  }
}

/**
 * 全局错误处理器实例
 */
export const globalErrorHandler = new ErrorHandler()

/**
 * 错误处理装饰器
 */
export function withErrorHandling(errorHandler: ErrorHandler = globalErrorHandler) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value

    descriptor.value = async function (...args: any[]) {
      const messageId = `${target.constructor.name}.${propertyKey}-${Date.now()}`
      let attempt = 0

      while (true) {
        try {
          const result = await originalMethod.apply(this, args)
          errorHandler.recordSuccess(messageId)
          return result
        } catch (error) {
          const context = {
            messageId,
            method: propertyKey,
            attempt,
            originalMessage: {} as IPCMessage // TODO: 从参数中提取
          }

          const { shouldRetry, delay, transformedError } = await errorHandler.handleError(error, context)

          if (!shouldRetry) {
            throw transformedError || error
          }

          if (delay) {
            await new Promise(resolve => setTimeout(resolve, delay))
          }

          attempt++
        }
      }
    }

    return descriptor
  }
}
