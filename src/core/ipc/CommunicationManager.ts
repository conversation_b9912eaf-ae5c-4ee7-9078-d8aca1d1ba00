/**
 * 核心通信管理器
 * 统一管理所有 IPC 通信，支持多种协议和高级功能
 */

import { EventEmitter } from 'events'
import {
	IPCMessage,
	ProtocolAdapter,
	CommunicationManagerConfig,
	MessageContext,
	ProcessType,
	MessageId,
	ProtocolType,
	MessagePriority,
	IPCError,
	IPCErrorType,
	MessageMetrics,
	PerformanceMetrics,
	DebugInfo
} from './types'
import { Logger } from './Logger'
import { MetricsCollector } from './MetricsCollector'

export class CommunicationManager extends EventEmitter {
	private config: Required<CommunicationManagerConfig>
	private adapters = new Map<ProtocolType, ProtocolAdapter>()
	private pendingMessages = new Map<
		MessageId,
		{
			resolve: (value: any) => void
			reject: (error: any) => void
			timeout: NodeJS.Timeout
			startTime: number
		}
	>()
	private messageQueue: IPCMessage[] = []
	private isProcessing = false
	private logger: Logger
	private metrics: MetricsCollector
	private messageIdCounter = 0

	constructor(config: CommunicationManagerConfig) {
		super()

		// 设置默认配置
		this.config = {
			processType: config.processType,
			enableLogging: config.enableLogging ?? true,
			enableMetrics: config.enableMetrics ?? true,
			defaultTimeout: config.defaultTimeout ?? 30000,
			maxRetries: config.maxRetries ?? 3,
			retryDelay: config.retryDelay ?? 1000,
			enableCompression: config.enableCompression ?? false,
			enableEncryption: config.enableEncryption ?? false
		}

		this.logger = new Logger(this.config.enableLogging)
		this.metrics = new MetricsCollector(this.config.enableMetrics)

		this.setupErrorHandling()
		this.startMessageProcessor()
	}

	/**
	 * 注册协议适配器
	 */
	registerAdapter(adapter: ProtocolAdapter): void {
		this.adapters.set(adapter.type, adapter)
		this.logger.info(`Registered adapter for protocol: ${adapter.type}`)
	}

	/**
	 * 注销协议适配器
	 */
	unregisterAdapter(type: ProtocolType): void {
		this.adapters.delete(type)
		this.logger.info(`Unregistered adapter for protocol: ${type}`)
	}

	/**
	 * 发送消息
	 */
	async send<T = any>(message: IPCMessage): Promise<T> {
		return new Promise((resolve, reject) => {
			try {
				// 验证消息
				this.validateMessage(message)

				// 添加到队列
				this.messageQueue.push(message)

				// 如果是需要响应的消息，设置回调
				if (this.needsResponse(message)) {
					const timeout = setTimeout(() => {
						this.pendingMessages.delete(message.id)
						reject(new IPCError(IPCErrorType.TIMEOUT, `Message timeout: ${message.id}`))
					}, this.config.defaultTimeout)

					this.pendingMessages.set(message.id, {
						resolve,
						reject,
						timeout,
						startTime: Date.now()
					})
				} else {
					resolve(undefined as T)
				}

				// 触发处理
				this.processMessageQueue()
			} catch (error) {
				reject(error)
			}
		})
	}

	/**
	 * 处理接收到的消息
	 */
	async handleMessage(message: IPCMessage, context: MessageContext): Promise<void> {
		try {
			this.logger.debug(`Handling message: ${message.type}:${message.id}`)
			this.metrics.recordMessage(message)

			// 检查是否是响应消息
			if (this.isResponseMessage(message)) {
				this.handleResponseMessage(message)
				return
			}

			// 查找适配器
			const adapter = this.findAdapter(message)
			if (!adapter) {
				throw new IPCError(
					IPCErrorType.METHOD_NOT_FOUND,
					`No adapter found for message type: ${message.type}`
				)
			}

			// 处理消息
			await adapter.process(message, context)
		} catch (error) {
			this.logger.error(`Error handling message ${message.id}:`, error)
			this.emit('error', error, message)

			// 发送错误响应
			if (this.needsResponse(message)) {
				const errorResponse = this.createErrorResponse(message, error)
				context.reply(errorResponse)
			}
		}
	}

	/**
	 * 生成唯一消息ID
	 */
	generateMessageId(): MessageId {
		const prefix = this.config.processType === 'main' ? 'M' : 'R'
		return `${prefix}${Date.now()}-${++this.messageIdCounter}`
	}

	/**
	 * 创建基础消息
	 */
	createBaseMessage(
		type: ProtocolType,
		priority: MessagePriority = 'normal'
	): Partial<IPCMessage> {
		return {
			id: this.generateMessageId(),
			type,
			timestamp: Date.now(),
			priority,
			source: this.config.processType
		}
	}

	/**
	 * 获取调试信息
	 */
	getDebugInfo(): DebugInfo {
		return {
			processType: this.config.processType,
			messageMetrics: this.metrics.getMessageMetrics(),
			performanceMetrics: this.metrics.getPerformanceMetrics(),
			activeStreams: [], // TODO: 实现流状态跟踪
			registeredMethods: [], // TODO: 从 RPC 适配器获取
			subscribedEvents: [] // TODO: 从事件适配器获取
		}
	}

	/**
	 * 清理资源
	 */
	dispose(): void {
		// 清理待处理的消息
		for (const [id, pending] of this.pendingMessages) {
			clearTimeout(pending.timeout)
			pending.reject(
				new IPCError(IPCErrorType.INTERNAL_ERROR, 'Communication manager disposed')
			)
		}
		this.pendingMessages.clear()

		// 清理适配器
		this.adapters.clear()

		// 清理队列
		this.messageQueue.length = 0

		this.logger.info('Communication manager disposed')
	}

	// ============= 私有方法 =============

	private validateMessage(message: IPCMessage): void {
		if (!message.id) {
			throw new IPCError(IPCErrorType.INVALID_PARAMS, 'Message ID is required')
		}
		if (!message.type) {
			throw new IPCError(IPCErrorType.INVALID_PARAMS, 'Message type is required')
		}
		if (!message.timestamp) {
			throw new IPCError(IPCErrorType.INVALID_PARAMS, 'Message timestamp is required')
		}
	}

	private needsResponse(message: IPCMessage): boolean {
		return message.type === ProtocolType.RPC && 'method' in message
	}

	private isResponseMessage(message: IPCMessage): boolean {
		return message.type === ProtocolType.RPC && 'requestId' in message
	}

	private findAdapter(message: IPCMessage): ProtocolAdapter | undefined {
		const adapter = this.adapters.get(message.type)
		return adapter?.canHandle(message) ? adapter : undefined
	}

	private handleResponseMessage(message: IPCMessage): void {
		if ('requestId' in message) {
			const pending = this.pendingMessages.get(message.requestId as string)
			if (pending) {
				clearTimeout(pending.timeout)
				this.pendingMessages.delete(message.requestId as string)

				// 记录响应时间
				const responseTime = Date.now() - pending.startTime
				this.metrics.recordResponseTime(responseTime)

				if ('error' in message && message.error) {
					pending.reject(
						new IPCError(
							message.error.code as IPCErrorType,
							message.error.message,
							message.error.details
						)
					)
				} else {
					pending.resolve('result' in message ? message.result : undefined)
				}
			}
		}
	}

	private createErrorResponse(originalMessage: IPCMessage, error: any): IPCMessage {
		return {
			...this.createBaseMessage(ProtocolType.RPC),
			requestId: originalMessage.id,
			error: {
				code: error.type || IPCErrorType.INTERNAL_ERROR,
				message: error.message || 'Unknown error',
				stack: error.stack,
				details: error.details
			}
		} as IPCMessage
	}

	private async processMessageQueue(): Promise<void> {
		if (this.isProcessing || this.messageQueue.length === 0) {
			return
		}

		this.isProcessing = true

		try {
			while (this.messageQueue.length > 0) {
				const message = this.messageQueue.shift()!
				await this.sendMessage(message)
			}
		} catch (error) {
			this.logger.error('Error processing message queue:', error)
		} finally {
			this.isProcessing = false
		}
	}

	private async sendMessage(message: IPCMessage): Promise<void> {
		// 这里应该根据进程类型调用相应的发送方法
		// 在实际实现中，这会通过 electron 的 ipcMain 或 ipcRenderer 发送
		this.emit('send', message)
	}

	private setupErrorHandling(): void {
		this.on('error', (error, message) => {
			this.logger.error('IPC Error:', error)
			this.metrics.recordError(error, message)
		})
	}

	private startMessageProcessor(): void {
		// 定期处理消息队列
		setInterval(() => {
			this.processMessageQueue()
		}, 10) // 10ms 间隔
	}
}
