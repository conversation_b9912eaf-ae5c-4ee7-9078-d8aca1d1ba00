/**
 * Electron IPC 通信架构 - 核心类型定义
 * 支持多种通信协议和类型安全
 */

// ============= 基础类型定义 =============

/** 消息唯一标识符 */
export type MessageId = string

/** 进程类型 */
export type ProcessType = 'main' | 'renderer'

/** 消息优先级 */
export type MessagePriority = 'low' | 'normal' | 'high' | 'critical'

/** 消息状态 */
export type MessageStatus = 'pending' | 'sent' | 'received' | 'success' | 'error' | 'timeout'

// ============= 协议类型定义 =============

/** 支持的通信协议类型 */
export enum ProtocolType {
	RPC = 'rpc', // 请求-响应协议
	EVENT = 'event', // 事件发布订阅协议
	STREAM = 'stream', // 流式数据传输协议
	FILE = 'file', // 文件传输协议
	BROADCAST = 'broadcast' // 广播协议
}

// ============= 消息基础结构 =============

/** 基础消息接口 */
export interface BaseMessage {
	id: MessageId
	type: ProtocolType
	timestamp: number
	priority: MessagePriority
	source: ProcessType
	target?: ProcessType
	metadata?: Record<string, any>
}

/** RPC 请求消息 */
export interface RPCRequestMessage extends BaseMessage {
	type: ProtocolType.RPC
	method: string
	params?: any[]
	timeout?: number
}

/** RPC 响应消息 */
export interface RPCResponseMessage extends BaseMessage {
	type: ProtocolType.RPC
	requestId: MessageId
	result?: any
	error?: {
		code: string
		message: string
		stack?: string
		details?: any
	}
}

/** 事件消息 */
export interface EventMessage extends BaseMessage {
	type: ProtocolType.EVENT
	event: string
	data?: any
	namespace?: string
}

/** 流式数据消息 */
export interface StreamMessage extends BaseMessage {
	type: ProtocolType.STREAM
	streamId: string
	chunk?: any
	isEnd?: boolean
	error?: string
}

/** 文件传输消息 */
export interface FileMessage extends BaseMessage {
	type: ProtocolType.FILE
	operation: 'upload' | 'download' | 'progress' | 'complete' | 'error'
	fileId: string
	fileName?: string
	fileSize?: number
	chunk?: Buffer
	progress?: number
	error?: string
}

/** 广播消息 */
export interface BroadcastMessage extends BaseMessage {
	type: ProtocolType.BROADCAST
	channel: string
	data?: any
}

/** 联合消息类型 */
export type IPCMessage =
	| RPCRequestMessage
	| RPCResponseMessage
	| EventMessage
	| StreamMessage
	| FileMessage
	| BroadcastMessage

// ============= 协议适配器接口 =============

/** 协议适配器基础接口 */
export interface ProtocolAdapter<T extends IPCMessage = IPCMessage> {
	readonly type: ProtocolType
	canHandle(message: IPCMessage): message is T
	process(message: T, context: MessageContext): Promise<void>
	serialize?(data: any): any
	deserialize?(data: any): any
}

/** 消息上下文 */
export interface MessageContext {
	sender?: Electron.WebContents
	reply: (message: IPCMessage) => void
	broadcast: (message: IPCMessage) => void
	forward: (target: string, message: IPCMessage) => void
}

// ============= 通信管理器接口 =============

/** 通信管理器配置 */
export interface CommunicationManagerConfig {
	processType: ProcessType
	enableLogging?: boolean
	enableMetrics?: boolean
	defaultTimeout?: number
	maxRetries?: number
	retryDelay?: number
	enableCompression?: boolean
	enableEncryption?: boolean
}

/** 消息处理器 */
export type MessageHandler<T = any> = (data: T, context: MessageContext) => Promise<any> | any

/** 事件监听器 */
export type EventListener<T = any> = (data: T, context: MessageContext) => void

/** 流数据处理器 */
export type StreamHandler<T = any> = (chunk: T, isEnd: boolean, context: MessageContext) => void

// ============= RPC 相关类型 =============

/** RPC 方法定义 */
export interface RPCMethodDefinition {
	name: string
	handler: MessageHandler
	timeout?: number
	retries?: number
	validation?: (params: any[]) => boolean
	description?: string
}

/** RPC 调用选项 */
export interface RPCCallOptions {
	timeout?: number
	retries?: number
	priority?: MessagePriority
	metadata?: Record<string, any>
}

// ============= 事件相关类型 =============

/** 事件订阅选项 */
export interface EventSubscriptionOptions {
	namespace?: string
	once?: boolean
	priority?: MessagePriority
	filter?: (data: any) => boolean
}

// ============= 流式传输相关类型 =============

/** 流配置 */
export interface StreamConfig {
	streamId: string
	chunkSize?: number
	timeout?: number
	compression?: boolean
	encryption?: boolean
}

/** 流状态 */
export interface StreamState {
	id: string
	isActive: boolean
	bytesTransferred: number
	totalBytes?: number
	startTime: number
	lastActivity: number
	error?: string
}

// ============= 文件传输相关类型 =============

/** 文件传输配置 */
export interface FileTransferConfig {
	fileId: string
	fileName: string
	fileSize: number
	chunkSize?: number
	compression?: boolean
	encryption?: boolean
	checksum?: string
}

/** 文件传输进度 */
export interface FileTransferProgress {
	fileId: string
	fileName: string
	totalBytes: number
	transferredBytes: number
	percentage: number
	speed: number // bytes per second
	estimatedTimeRemaining: number // seconds
}

// ============= 监控和调试相关类型 =============

/** 消息统计信息 */
export interface MessageMetrics {
	totalMessages: number
	messagesByType: Record<ProtocolType, number>
	averageResponseTime: number
	errorRate: number
	throughput: number // messages per second
}

/** 性能指标 */
export interface PerformanceMetrics {
	memoryUsage: NodeJS.MemoryUsage
	cpuUsage: number
	messageQueueSize: number
	activeConnections: number
	uptime: number
}

/** 调试信息 */
export interface DebugInfo {
	processType: ProcessType
	messageMetrics: MessageMetrics
	performanceMetrics: PerformanceMetrics
	activeStreams: StreamState[]
	registeredMethods: string[]
	subscribedEvents: string[]
}

// ============= 错误处理相关类型 =============

/** IPC 错误类型 */
export enum IPCErrorType {
	TIMEOUT = 'TIMEOUT',
	METHOD_NOT_FOUND = 'METHOD_NOT_FOUND',
	INVALID_PARAMS = 'INVALID_PARAMS',
	SERIALIZATION_ERROR = 'SERIALIZATION_ERROR',
	NETWORK_ERROR = 'NETWORK_ERROR',
	PERMISSION_DENIED = 'PERMISSION_DENIED',
	RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
	INTERNAL_ERROR = 'INTERNAL_ERROR'
}

/** IPC 错误 */
export class IPCError extends Error {
	constructor(
		public type: IPCErrorType,
		message: string,
		public details?: any,
		public code?: string
	) {
		super(message)
		this.name = 'IPCError'
	}
}

// ============= 工具类型 =============

/** 深度只读 */
export type DeepReadonly<T> = {
	readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P]
}

/** 可选字段 */
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

/** 提取消息数据类型 */
export type ExtractMessageData<T extends IPCMessage> = T extends EventMessage
	? T['data']
	: T extends StreamMessage
		? T['chunk']
		: T extends FileMessage
			? T['chunk']
			: T extends BroadcastMessage
				? T['data']
				: never
