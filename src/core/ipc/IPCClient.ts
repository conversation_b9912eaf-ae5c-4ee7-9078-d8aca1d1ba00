/**
 * IPC 客户端 API (渲染进程端)
 * 提供类型安全的通信接口
 */

import { EventEmitter } from 'events'
import { CommunicationManager } from './CommunicationManager'
import { RPCAdapter } from './adapters/RPCAdapter'
import { EventAdapter } from './adapters/EventAdapter'
import { StreamAdapter } from './adapters/StreamAdapter'
import { FileAdapter } from './adapters/FileAdapter'
import {
	CommunicationManagerConfig,
	RPCCallOptions,
	EventSubscriptionOptions,
	StreamConfig,
	FileTransferConfig,
	MessageContext,
	EventListener,
	StreamHandler,
	MessageHandler,
	ProtocolType,
	IPCMessage
} from './types'

export class IPCClient extends EventEmitter {
	private manager: CommunicationManager
	private rpcAdapter: RPCAdapter
	private eventAdapter: EventAdapter
	private streamAdapter: StreamAdapter
	private fileAdapter: FileAdapter

	constructor(config?: Partial<CommunicationManagerConfig>) {
		super()

		const fullConfig: CommunicationManagerConfig = {
			processType: 'renderer',
			enableLogging: true,
			enableMetrics: true,
			defaultTimeout: 30000,
			maxRetries: 3,
			retryDelay: 1000,
			...config
		}

		this.manager = new CommunicationManager(fullConfig)
		this.rpcAdapter = new RPCAdapter()
		this.eventAdapter = new EventAdapter()
		this.streamAdapter = new StreamAdapter()
		this.fileAdapter = new FileAdapter()

		this.setupAdapters()
		this.setupEventForwarding()
	}

	// ============= RPC 方法 =============

	/**
	 * 调用远程方法
	 */
	async call<TResult = any>(
		method: string,
		params?: any[],
		options?: RPCCallOptions
	): Promise<TResult> {
		return this.rpcAdapter.call(method, params, options)
	}

	/**
	 * 注册本地方法供远程调用
	 */
	registerMethod(
		name: string,
		handler: MessageHandler,
		options?: {
			timeout?: number
			retries?: number
			validation?: (params: any[]) => boolean
			description?: string
		}
	): void {
		this.rpcAdapter.registerMethod({
			name,
			handler,
			...options
		})
	}

	/**
	 * 注销本地方法
	 */
	unregisterMethod(name: string): void {
		this.rpcAdapter.unregisterMethod(name)
	}

	// ============= 事件方法 =============

	/**
	 * 订阅事件
	 */
	subscribe<TData = any>(
		event: string,
		listener: EventListener<TData>,
		options?: EventSubscriptionOptions
	): string {
		return this.eventAdapter.subscribe(event, listener, options)
	}

	/**
	 * 取消订阅
	 */
	unsubscribe(subscriptionId: string): boolean {
		return this.eventAdapter.unsubscribe(subscriptionId)
	}

	/**
	 * 发布事件
	 */
	publish<TData = any>(event: string, data?: TData, namespace?: string): void {
		this.eventAdapter.publish(event, data, namespace)
	}

	/**
	 * 一次性事件监听
	 */
	once<TData = any>(
		event: string,
		listener: EventListener<TData>,
		options?: Omit<EventSubscriptionOptions, 'once'>
	): string {
		return this.eventAdapter.subscribe(event, listener, { ...options, once: true })
	}

	// ============= 流式传输方法 =============

	/**
	 * 创建数据流
	 */
	createStream<TData = any>(config: StreamConfig, handler: StreamHandler<TData>): string {
		return this.streamAdapter.createStream(config, handler)
	}

	/**
	 * 发送流数据块
	 */
	sendStreamChunk(streamId: string, chunk: any, isEnd?: boolean): void {
		this.streamAdapter.sendChunk(streamId, chunk, isEnd)
	}

	/**
	 * 结束流
	 */
	endStream(streamId: string, error?: string): void {
		this.streamAdapter.endStream(streamId, error)
	}

	// ============= 文件传输方法 =============

	/**
	 * 上传文件
	 */
	async uploadFile(
		file: File,
		options?: {
			chunkSize?: number
			onProgress?: (progress: number) => void
		}
	): Promise<string> {
		const fileId = `upload-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
		const chunkSize = options?.chunkSize || 64 * 1024

		const config: FileTransferConfig = {
			fileId,
			fileName: file.name,
			fileSize: file.size,
			chunkSize
		}

		this.fileAdapter.startUpload(config)

		// 监听进度事件
		if (options?.onProgress) {
			this.fileAdapter.on('uploadProgress', event => {
				if (event.fileId === fileId) {
					options.onProgress!(event.progress.percentage)
				}
			})
		}

		// 分块读取并发送文件
		let offset = 0
		while (offset < file.size) {
			const chunk = file.slice(offset, offset + chunkSize)
			const buffer = await this.fileToBuffer(chunk)

			this.fileAdapter.sendFileChunk(fileId, buffer)
			offset += chunkSize
		}

		this.fileAdapter.completeTransfer(fileId)
		return fileId
	}

	/**
	 * 取消文件传输
	 */
	cancelFileTransfer(fileId: string, reason?: string): void {
		this.fileAdapter.cancelTransfer(fileId, reason)
	}

	// ============= 工具方法 =============

	/**
	 * 获取调试信息
	 */
	getDebugInfo() {
		return this.manager.getDebugInfo()
	}

	/**
	 * 获取连接状态
	 */
	isConnected(): boolean {
		// TODO: 实现连接状态检查
		return true
	}

	/**
	 * 重新连接
	 */
	async reconnect(): Promise<void> {
		// TODO: 实现重连逻辑
	}

	/**
	 * 清理资源
	 */
	dispose(): void {
		this.rpcAdapter.dispose()
		this.eventAdapter.dispose()
		this.streamAdapter.dispose()
		this.fileAdapter.dispose()
		this.manager.dispose()
		this.removeAllListeners()
	}

	// ============= 私有方法 =============

	private setupAdapters(): void {
		this.manager.registerAdapter(this.rpcAdapter)
		this.manager.registerAdapter(this.eventAdapter)
		this.manager.registerAdapter(this.streamAdapter)
		this.manager.registerAdapter(this.fileAdapter)
	}

	private setupEventForwarding(): void {
		// 转发 RPC 调用
		this.rpcAdapter.on('call', message => {
			this.manager.send(message)
		})

		// 转发事件发布
		this.eventAdapter.on('publish', message => {
			this.manager.send(message)
		})

		// 转发流数据
		this.streamAdapter.on('sendChunk', message => {
			this.manager.send(message)
		})

		this.streamAdapter.on('endStream', message => {
			this.manager.send(message)
		})

		// 转发文件传输
		this.fileAdapter.on('sendChunk', message => {
			this.manager.send(message)
		})

		this.fileAdapter.on('completeTransfer', message => {
			this.manager.send(message)
		})

		this.fileAdapter.on('cancelTransfer', message => {
			this.manager.send(message)
		})

		// 转发错误事件
		this.manager.on('error', (error, message) => {
			this.emit('error', error, message)
		})
	}

	private async fileToBuffer(file: Blob): Promise<Buffer> {
		return new Promise((resolve, reject) => {
			const reader = new FileReader()
			reader.onload = () => {
				const arrayBuffer = reader.result as ArrayBuffer
				resolve(Buffer.from(arrayBuffer))
			}
			reader.onerror = reject
			reader.readAsArrayBuffer(file)
		})
	}

	/**
	 * 处理来自主进程的消息
	 */
	handleMessage(message: IPCMessage, context: MessageContext): void {
		this.manager.handleMessage(message, context)
	}
}
