/**
 * IPC 通信指标收集器
 * 收集性能指标和统计信息
 */

import { IPCMessage, ProtocolType, MessageMetrics, PerformanceMetrics } from './types'

interface MessageStats {
	count: number
	totalResponseTime: number
	errors: number
	lastActivity: number
}

export class MetricsCollector {
	private enabled: boolean
	private startTime: number = Date.now()
	private messageStats = new Map<ProtocolType, MessageStats>()
	private responseTimes: number[] = []
	private maxResponseTimes: number = 1000
	private errorCount: number = 0
	private totalMessages: number = 0

	constructor(enabled: boolean = true) {
		this.enabled = enabled
		this.initializeStats()
	}

	/**
	 * 记录消息
	 */
	recordMessage(message: IPCMessage): void {
		if (!this.enabled) return

		this.totalMessages++

		const stats = this.messageStats.get(message.type)
		if (stats) {
			stats.count++
			stats.lastActivity = Date.now()
		}
	}

	/**
	 * 记录响应时间
	 */
	recordResponseTime(responseTime: number): void {
		if (!this.enabled) return

		this.responseTimes.push(responseTime)
		if (this.responseTimes.length > this.maxResponseTimes) {
			this.responseTimes.shift()
		}
	}

	/**
	 * 记录错误
	 */
	recordError(error: any, message?: IPCMessage): void {
		if (!this.enabled) return

		this.errorCount++

		if (message) {
			const stats = this.messageStats.get(message.type)
			if (stats) {
				stats.errors++
			}
		}
	}

	/**
	 * 获取消息统计
	 */
	getMessageMetrics(): MessageMetrics {
		const messagesByType: Record<ProtocolType, number> = {} as any

		for (const [type, stats] of this.messageStats) {
			messagesByType[type] = stats.count
		}

		const averageResponseTime =
			this.responseTimes.length > 0
				? this.responseTimes.reduce((sum, time) => sum + time, 0) /
					this.responseTimes.length
				: 0

		const errorRate = this.totalMessages > 0 ? this.errorCount / this.totalMessages : 0

		const uptime = Date.now() - this.startTime
		const throughput = uptime > 0 ? (this.totalMessages / uptime) * 1000 : 0

		return {
			totalMessages: this.totalMessages,
			messagesByType,
			averageResponseTime,
			errorRate,
			throughput
		}
	}

	/**
	 * 获取性能指标
	 */
	getPerformanceMetrics(): PerformanceMetrics {
		const memoryUsage = process.memoryUsage()
		const uptime = Date.now() - this.startTime

		return {
			memoryUsage,
			cpuUsage: this.getCpuUsage(),
			messageQueueSize: 0, // TODO: 从通信管理器获取
			activeConnections: 0, // TODO: 从通信管理器获取
			uptime
		}
	}

	/**
	 * 重置统计信息
	 */
	reset(): void {
		this.totalMessages = 0
		this.errorCount = 0
		this.responseTimes.length = 0
		this.startTime = Date.now()
		this.initializeStats()
	}

	/**
	 * 获取详细统计信息
	 */
	getDetailedStats() {
		const stats: any = {}

		for (const [type, messageStats] of this.messageStats) {
			stats[type] = {
				count: messageStats.count,
				errors: messageStats.errors,
				errorRate: messageStats.count > 0 ? messageStats.errors / messageStats.count : 0,
				averageResponseTime:
					messageStats.count > 0
						? messageStats.totalResponseTime / messageStats.count
						: 0,
				lastActivity: messageStats.lastActivity
			}
		}

		return {
			overview: this.getMessageMetrics(),
			performance: this.getPerformanceMetrics(),
			byProtocol: stats,
			responseTimes: {
				min: Math.min(...this.responseTimes),
				max: Math.max(...this.responseTimes),
				median: this.getMedian(this.responseTimes),
				p95: this.getPercentile(this.responseTimes, 0.95),
				p99: this.getPercentile(this.responseTimes, 0.99)
			}
		}
	}

	private initializeStats(): void {
		for (const type of Object.values(ProtocolType)) {
			this.messageStats.set(type, {
				count: 0,
				totalResponseTime: 0,
				errors: 0,
				lastActivity: 0
			})
		}
	}

	private getCpuUsage(): number {
		// 简化的 CPU 使用率计算
		// 在实际应用中，可能需要更复杂的实现
		try {
			const usage = process.cpuUsage()
			return (usage.user + usage.system) / 1000000 // 转换为秒
		} catch {
			return 0
		}
	}

	private getMedian(numbers: number[]): number {
		if (numbers.length === 0) return 0

		const sorted = [...numbers].sort((a, b) => a - b)
		const mid = Math.floor(sorted.length / 2)

		return sorted.length % 2 === 0 ? (sorted[mid - 1] + sorted[mid]) / 2 : sorted[mid]
	}

	private getPercentile(numbers: number[], percentile: number): number {
		if (numbers.length === 0) return 0

		const sorted = [...numbers].sort((a, b) => a - b)
		const index = Math.ceil(sorted.length * percentile) - 1

		return sorted[Math.max(0, index)]
	}
}
