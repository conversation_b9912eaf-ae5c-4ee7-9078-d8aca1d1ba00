/**
 * RPC 协议适配器
 * 处理请求-响应模式的通信
 */

import { EventEmitter } from 'events'
import {
	ProtocolAdapter,
	ProtocolType,
	IPCMessage,
	RPCRequestMessage,
	RPCResponseMessage,
	MessageContext,
	MessageHandler,
	RPCMethodDefinition,
	RPCCallOptions,
	IPCError,
	IPCErrorType
} from '../types'

export class RPC<PERSON>dapter
	extends EventEmitter
	implements ProtocolAdapter<RPCRequestMessage | RPCResponseMessage>
{
	readonly type = ProtocolType.RPC

	private methods = new Map<string, RPCMethodDefinition>()
	private pendingCalls = new Map<
		string,
		{
			resolve: (value: any) => void
			reject: (error: any) => void
			timeout: NodeJS.Timeout
			retryCount: number
		}
	>()

	/**
	 * 注册 RPC 方法
	 */
	registerMethod(definition: RPCMethodDefinition): void {
		this.methods.set(definition.name, definition)
		this.emit('methodRegistered', definition.name)
	}

	/**
	 * 注销 RPC 方法
	 */
	unregisterMethod(name: string): void {
		this.methods.delete(name)
		this.emit('methodUnregistered', name)
	}

	/**
	 * 获取已注册的方法列表
	 */
	getRegisteredMethods(): string[] {
		return Array.from(this.methods.keys())
	}

	/**
	 * 调用远程方法
	 */
	async call(method: string, params: any[] = [], options: RPCCallOptions = {}): Promise<any> {
		return new Promise((resolve, reject) => {
			const messageId = this.generateCallId()
			const timeout = options.timeout || 30000
			const maxRetries = options.retries || 3

			const timeoutHandle = setTimeout(() => {
				this.pendingCalls.delete(messageId)
				reject(new IPCError(IPCErrorType.TIMEOUT, `RPC call timeout: ${method}`))
			}, timeout)

			this.pendingCalls.set(messageId, {
				resolve,
				reject,
				timeout: timeoutHandle,
				retryCount: 0
			})

			const message: RPCRequestMessage = {
				id: messageId,
				type: ProtocolType.RPC,
				timestamp: Date.now(),
				priority: options.priority || 'normal',
				source: 'renderer', // TODO: 从配置获取
				method,
				params,
				timeout,
				metadata: options.metadata
			}

			this.emit('call', message)
		})
	}

	/**
	 * 检查是否可以处理消息
	 */
	canHandle(message: IPCMessage): message is RPCRequestMessage | RPCResponseMessage {
		return message.type === ProtocolType.RPC
	}

	/**
	 * 处理消息
	 */
	async process(
		message: RPCRequestMessage | RPCResponseMessage,
		context: MessageContext
	): Promise<void> {
		if (this.isRequestMessage(message)) {
			await this.handleRequest(message, context)
		} else if (this.isResponseMessage(message)) {
			this.handleResponse(message)
		}
	}

	/**
	 * 序列化数据
	 */
	serialize(data: any): any {
		try {
			return JSON.parse(JSON.stringify(data))
		} catch (error) {
			throw new IPCError(IPCErrorType.SERIALIZATION_ERROR, 'Failed to serialize RPC data')
		}
	}

	/**
	 * 反序列化数据
	 */
	deserialize(data: any): any {
		return data // JSON 数据不需要特殊反序列化
	}

	// ============= 私有方法 =============

	private isRequestMessage(
		message: RPCRequestMessage | RPCResponseMessage
	): message is RPCRequestMessage {
		return 'method' in message
	}

	private isResponseMessage(
		message: RPCRequestMessage | RPCResponseMessage
	): message is RPCResponseMessage {
		return 'requestId' in message
	}

	private async handleRequest(
		message: RPCRequestMessage,
		context: MessageContext
	): Promise<void> {
		const methodDef = this.methods.get(message.method)

		if (!methodDef) {
			const errorResponse: RPCResponseMessage = {
				id: this.generateResponseId(),
				type: ProtocolType.RPC,
				timestamp: Date.now(),
				priority: message.priority,
				source: 'main', // TODO: 从配置获取
				requestId: message.id,
				error: {
					code: IPCErrorType.METHOD_NOT_FOUND,
					message: `Method not found: ${message.method}`
				}
			}
			context.reply(errorResponse)
			return
		}

		try {
			// 验证参数
			if (methodDef.validation && !methodDef.validation(message.params || [])) {
				throw new IPCError(
					IPCErrorType.INVALID_PARAMS,
					`Invalid parameters for method: ${message.method}`
				)
			}

			// 执行方法
			const result = await methodDef.handler(message.params || [], context)

			// 发送成功响应
			const successResponse: RPCResponseMessage = {
				id: this.generateResponseId(),
				type: ProtocolType.RPC,
				timestamp: Date.now(),
				priority: message.priority,
				source: 'main', // TODO: 从配置获取
				requestId: message.id,
				result: this.serialize(result)
			}

			context.reply(successResponse)
		} catch (error) {
			// 发送错误响应
			const errorResponse: RPCResponseMessage = {
				id: this.generateResponseId(),
				type: ProtocolType.RPC,
				timestamp: Date.now(),
				priority: message.priority,
				source: 'main', // TODO: 从配置获取
				requestId: message.id,
				error: {
					code: error.type || IPCErrorType.INTERNAL_ERROR,
					message: error.message || 'Unknown error',
					stack: error.stack,
					details: error.details
				}
			}

			context.reply(errorResponse)
		}
	}

	private handleResponse(message: RPCResponseMessage): void {
		const pending = this.pendingCalls.get(message.requestId)

		if (!pending) {
			// 可能是超时后收到的响应，忽略
			return
		}

		clearTimeout(pending.timeout)
		this.pendingCalls.delete(message.requestId)

		if (message.error) {
			const error = new IPCError(
				message.error.code as IPCErrorType,
				message.error.message,
				message.error.details
			)
			pending.reject(error)
		} else {
			pending.resolve(this.deserialize(message.result))
		}
	}

	private generateCallId(): string {
		return `rpc-call-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
	}

	private generateResponseId(): string {
		return `rpc-response-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
	}

	/**
	 * 清理资源
	 */
	dispose(): void {
		// 清理待处理的调用
		for (const [id, pending] of this.pendingCalls) {
			clearTimeout(pending.timeout)
			pending.reject(new IPCError(IPCErrorType.INTERNAL_ERROR, 'RPC adapter disposed'))
		}
		this.pendingCalls.clear()

		// 清理方法注册
		this.methods.clear()

		this.removeAllListeners()
	}
}
