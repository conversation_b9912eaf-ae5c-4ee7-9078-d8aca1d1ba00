/**
 * 流式协议适配器
 * 处理流式数据传输
 */

import { EventEmitter } from 'events'
import {
	ProtocolAdapter,
	ProtocolType,
	IPCMessage,
	StreamMessage,
	MessageContext,
	StreamHandler,
	StreamConfig,
	StreamState
} from '../types'

interface ActiveStream {
	id: string
	config: StreamConfig
	handler: StreamHandler
	state: StreamState
	chunks: any[]
	lastActivity: number
}

export class StreamAdapter extends EventEmitter implements ProtocolAdapter<StreamMessage> {
	readonly type = ProtocolType.STREAM

	private activeStreams = new Map<string, ActiveStream>()
	private streamTimeout = 30000 // 30 seconds default timeout
	private cleanupInterval: NodeJS.Timeout

	constructor() {
		super()

		// 定期清理超时的流
		this.cleanupInterval = setInterval(() => {
			this.cleanupTimeoutStreams()
		}, 10000) // 每10秒检查一次
	}

	/**
	 * 创建新的流
	 */
	createStream(config: StreamConfig, handler: StreamHandler): string {
		const stream: ActiveStream = {
			id: config.streamId,
			config,
			handler,
			state: {
				id: config.streamId,
				isActive: true,
				bytesTransferred: 0,
				startTime: Date.now(),
				lastActivity: Date.now()
			},
			chunks: [],
			lastActivity: Date.now()
		}

		this.activeStreams.set(config.streamId, stream)

		this.emit('streamCreated', {
			streamId: config.streamId,
			config
		})

		return config.streamId
	}

	/**
	 * 发送流数据块
	 */
	sendChunk(streamId: string, chunk: any, isEnd: boolean = false): void {
		const message: StreamMessage = {
			id: this.generateMessageId(),
			type: ProtocolType.STREAM,
			timestamp: Date.now(),
			priority: 'normal',
			source: 'renderer', // TODO: 从配置获取
			streamId,
			chunk,
			isEnd
		}

		this.emit('sendChunk', message)
	}

	/**
	 * 结束流
	 */
	endStream(streamId: string, error?: string): void {
		const message: StreamMessage = {
			id: this.generateMessageId(),
			type: ProtocolType.STREAM,
			timestamp: Date.now(),
			priority: 'normal',
			source: 'renderer', // TODO: 从配置获取
			streamId,
			isEnd: true,
			error
		}

		this.emit('endStream', message)
		this.closeStream(streamId)
	}

	/**
	 * 获取流状态
	 */
	getStreamState(streamId: string): StreamState | undefined {
		const stream = this.activeStreams.get(streamId)
		return stream?.state
	}

	/**
	 * 获取所有活跃流的状态
	 */
	getAllStreamStates(): StreamState[] {
		return Array.from(this.activeStreams.values()).map(stream => stream.state)
	}

	/**
	 * 检查是否可以处理消息
	 */
	canHandle(message: IPCMessage): message is StreamMessage {
		return message.type === ProtocolType.STREAM
	}

	/**
	 * 处理消息
	 */
	async process(message: StreamMessage, context: MessageContext): Promise<void> {
		await this.handleStreamMessage(message, context)
	}

	/**
	 * 序列化数据
	 */
	serialize(data: any): any {
		// 对于流数据，可能需要特殊的序列化处理
		if (Buffer.isBuffer(data)) {
			return {
				type: 'Buffer',
				data: Array.from(data)
			}
		}

		try {
			return JSON.parse(JSON.stringify(data))
		} catch (error) {
			throw new Error('Failed to serialize stream data')
		}
	}

	/**
	 * 反序列化数据
	 */
	deserialize(data: any): any {
		if (data && data.type === 'Buffer' && Array.isArray(data.data)) {
			return Buffer.from(data.data)
		}
		return data
	}

	// ============= 私有方法 =============

	private async handleStreamMessage(
		message: StreamMessage,
		context: MessageContext
	): Promise<void> {
		const stream = this.activeStreams.get(message.streamId)

		if (!stream) {
			// 流不存在，可能已经关闭或超时
			this.emit('streamNotFound', { streamId: message.streamId })
			return
		}

		// 更新活动时间
		stream.lastActivity = Date.now()
		stream.state.lastActivity = Date.now()

		try {
			if (message.error) {
				// 处理错误
				stream.state.error = message.error
				stream.state.isActive = false

				await stream.handler(null, true, context)
				this.closeStream(message.streamId)
			} else if (message.chunk !== undefined) {
				// 处理数据块
				const deserializedChunk = this.deserialize(message.chunk)
				stream.chunks.push(deserializedChunk)

				// 更新传输字节数（粗略估算）
				const chunkSize = this.estimateSize(deserializedChunk)
				stream.state.bytesTransferred += chunkSize

				await stream.handler(deserializedChunk, message.isEnd || false, context)

				if (message.isEnd) {
					stream.state.isActive = false
					this.closeStream(message.streamId)
				}
			}

			this.emit('streamProgress', {
				streamId: message.streamId,
				state: stream.state
			})
		} catch (error) {
			this.emit('streamError', {
				streamId: message.streamId,
				error,
				message
			})

			stream.state.error = error.message
			stream.state.isActive = false
			this.closeStream(message.streamId)
		}
	}

	private closeStream(streamId: string): void {
		const stream = this.activeStreams.get(streamId)
		if (stream) {
			stream.state.isActive = false
			this.activeStreams.delete(streamId)

			this.emit('streamClosed', {
				streamId,
				state: stream.state,
				totalChunks: stream.chunks.length
			})
		}
	}

	private cleanupTimeoutStreams(): void {
		const now = Date.now()
		const timeoutStreams: string[] = []

		for (const [streamId, stream] of this.activeStreams) {
			if (now - stream.lastActivity > this.streamTimeout) {
				timeoutStreams.push(streamId)
			}
		}

		for (const streamId of timeoutStreams) {
			const stream = this.activeStreams.get(streamId)
			if (stream) {
				stream.state.error = 'Stream timeout'
				stream.state.isActive = false
				this.closeStream(streamId)

				this.emit('streamTimeout', { streamId })
			}
		}
	}

	private estimateSize(data: any): number {
		if (Buffer.isBuffer(data)) {
			return data.length
		}

		if (typeof data === 'string') {
			return Buffer.byteLength(data, 'utf8')
		}

		try {
			return Buffer.byteLength(JSON.stringify(data), 'utf8')
		} catch {
			return 0
		}
	}

	private generateMessageId(): string {
		return `stream-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
	}

	/**
	 * 设置流超时时间
	 */
	setStreamTimeout(timeout: number): void {
		this.streamTimeout = timeout
	}

	/**
	 * 清理资源
	 */
	dispose(): void {
		// 关闭所有活跃流
		for (const streamId of this.activeStreams.keys()) {
			this.closeStream(streamId)
		}

		// 清理定时器
		if (this.cleanupInterval) {
			clearInterval(this.cleanupInterval)
		}

		this.removeAllListeners()
	}
}
