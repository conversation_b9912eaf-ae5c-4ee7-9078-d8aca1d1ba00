/**
 * 事件协议适配器
 * 处理发布-订阅模式的通信
 */

import { EventEmitter } from 'events'
import {
	ProtocolAdapter,
	ProtocolType,
	IPCMessage,
	EventMessage,
	MessageContext,
	EventListener,
	EventSubscriptionOptions
} from '../types'

interface EventSubscription {
	listener: EventListener
	options: EventSubscriptionOptions
	id: string
	createdAt: number
}

export class EventAdapter extends EventEmitter implements ProtocolAdapter<EventMessage> {
	readonly type = ProtocolType.EVENT

	private subscriptions = new Map<string, EventSubscription[]>()
	private namespaces = new Set<string>()
	private subscriptionCounter = 0

	/**
	 * 订阅事件
	 */
	subscribe(
		event: string,
		listener: EventListener,
		options: EventSubscriptionOptions = {}
	): string {
		const subscriptionId = this.generateSubscriptionId()
		const subscription: EventSubscription = {
			listener,
			options,
			id: subscriptionId,
			createdAt: Date.now()
		}

		const eventKey = this.getEventKey(event, options.namespace)

		if (!this.subscriptions.has(eventKey)) {
			this.subscriptions.set(eventKey, [])
		}

		this.subscriptions.get(eventKey)!.push(subscription)

		if (options.namespace) {
			this.namespaces.add(options.namespace)
		}

		this.emit('subscribed', { event, namespace: options.namespace, subscriptionId })

		return subscriptionId
	}

	/**
	 * 取消订阅
	 */
	unsubscribe(subscriptionId: string): boolean {
		for (const [eventKey, subscriptions] of this.subscriptions) {
			const index = subscriptions.findIndex(sub => sub.id === subscriptionId)
			if (index !== -1) {
				subscriptions.splice(index, 1)

				// 如果没有更多订阅，删除事件键
				if (subscriptions.length === 0) {
					this.subscriptions.delete(eventKey)
				}

				this.emit('unsubscribed', { subscriptionId })
				return true
			}
		}
		return false
	}

	/**
	 * 取消事件的所有订阅
	 */
	unsubscribeAll(event: string, namespace?: string): number {
		const eventKey = this.getEventKey(event, namespace)
		const subscriptions = this.subscriptions.get(eventKey)

		if (!subscriptions) {
			return 0
		}

		const count = subscriptions.length
		this.subscriptions.delete(eventKey)

		this.emit('unsubscribedAll', { event, namespace, count })
		return count
	}

	/**
	 * 发布事件
	 */
	publish(event: string, data?: any, namespace?: string): void {
		const message: EventMessage = {
			id: this.generateEventId(),
			type: ProtocolType.EVENT,
			timestamp: Date.now(),
			priority: 'normal',
			source: 'renderer', // TODO: 从配置获取
			event,
			data,
			namespace
		}

		this.emit('publish', message)
	}

	/**
	 * 获取订阅统计
	 */
	getSubscriptionStats(): {
		totalSubscriptions: number
		eventCount: number
		namespaceCount: number
		subscriptionsByEvent: Record<string, number>
	} {
		let totalSubscriptions = 0
		const subscriptionsByEvent: Record<string, number> = {}

		for (const [eventKey, subscriptions] of this.subscriptions) {
			totalSubscriptions += subscriptions.length
			subscriptionsByEvent[eventKey] = subscriptions.length
		}

		return {
			totalSubscriptions,
			eventCount: this.subscriptions.size,
			namespaceCount: this.namespaces.size,
			subscriptionsByEvent
		}
	}

	/**
	 * 获取已订阅的事件列表
	 */
	getSubscribedEvents(): string[] {
		return Array.from(this.subscriptions.keys())
	}

	/**
	 * 检查是否可以处理消息
	 */
	canHandle(message: IPCMessage): message is EventMessage {
		return message.type === ProtocolType.EVENT
	}

	/**
	 * 处理消息
	 */
	async process(message: EventMessage, context: MessageContext): Promise<void> {
		await this.handleEvent(message, context)
	}

	/**
	 * 序列化数据
	 */
	serialize(data: any): any {
		try {
			return JSON.parse(JSON.stringify(data))
		} catch (error) {
			throw new Error('Failed to serialize event data')
		}
	}

	/**
	 * 反序列化数据
	 */
	deserialize(data: any): any {
		return data
	}

	// ============= 私有方法 =============

	private async handleEvent(message: EventMessage, context: MessageContext): Promise<void> {
		const eventKey = this.getEventKey(message.event, message.namespace)
		const subscriptions = this.subscriptions.get(eventKey) || []

		// 同时处理没有命名空间的通用订阅
		const generalEventKey = this.getEventKey(message.event)
		const generalSubscriptions = message.namespace
			? this.subscriptions.get(generalEventKey) || []
			: []

		const allSubscriptions = [...subscriptions, ...generalSubscriptions]

		if (allSubscriptions.length === 0) {
			return
		}

		// 并行处理所有订阅
		const promises = allSubscriptions.map(async subscription => {
			try {
				// 应用过滤器
				if (subscription.options.filter && !subscription.options.filter(message.data)) {
					return
				}

				// 调用监听器
				await subscription.listener(message.data, context)

				// 如果是一次性订阅，移除它
				if (subscription.options.once) {
					this.unsubscribe(subscription.id)
				}
			} catch (error) {
				this.emit('listenerError', {
					error,
					subscription,
					event: message.event,
					data: message.data
				})
			}
		})

		await Promise.allSettled(promises)

		this.emit('eventProcessed', {
			event: message.event,
			namespace: message.namespace,
			listenerCount: allSubscriptions.length
		})
	}

	private getEventKey(event: string, namespace?: string): string {
		return namespace ? `${namespace}:${event}` : event
	}

	private generateSubscriptionId(): string {
		return `sub-${Date.now()}-${++this.subscriptionCounter}`
	}

	private generateEventId(): string {
		return `event-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
	}

	/**
	 * 清理资源
	 */
	dispose(): void {
		this.subscriptions.clear()
		this.namespaces.clear()
		this.removeAllListeners()
	}
}
