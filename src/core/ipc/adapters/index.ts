/**
 * 协议适配器导出
 */

export { RPCAdapter } from './RPCAdapter'
export { EventAdapter } from './EventAdapter'
export { StreamAdapter } from './StreamAdapter'
export { FileAdapter } from './FileAdapter'

// 适配器工厂
import { ProtocolType, ProtocolAdapter } from '../types'
import { RPCAdapter } from './RPCAdapter'
import { EventAdapter } from './EventAdapter'
import { StreamAdapter } from './StreamAdapter'
import { FileAdapter } from './FileAdapter'

export function createAdapter(type: ProtocolType): ProtocolAdapter {
	switch (type) {
		case ProtocolType.RPC:
			return new RPCAdapter()
		case ProtocolType.EVENT:
			return new EventAdapter()
		case ProtocolType.STREAM:
			return new StreamAdapter()
		case ProtocolType.FILE:
			return new FileAdapter()
		default:
			throw new Error(`Unsupported protocol type: ${type}`)
	}
}

export function createAllAdapters(): ProtocolAdapter[] {
	return [new RPCAdapter(), new EventAdapter(), new StreamAdapter(), new FileAdapter()]
}
