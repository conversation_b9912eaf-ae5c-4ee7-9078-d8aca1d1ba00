/**
 * 文件传输协议适配器
 * 处理文件上传和下载
 */

import { EventEmitter } from 'events'
import { createHash } from 'crypto'
import {
  ProtocolAdapter,
  ProtocolType,
  IPCMessage,
  FileMessage,
  MessageContext,
  FileTransferConfig,
  FileTransferProgress
} from '../types'

interface ActiveFileTransfer {
  id: string
  config: FileTransferConfig
  progress: FileTransferProgress
  chunks: Buffer[]
  startTime: number
  lastActivity: number
  checksum?: string
}

export class FileAdapter extends EventEmitter implements ProtocolAdapter<FileMessage> {
  readonly type = ProtocolType.FILE
  
  private activeTransfers = new Map<string, ActiveFileTransfer>()
  private defaultChunkSize = 64 * 1024 // 64KB
  private transferTimeout = 60000 // 60 seconds
  private cleanupInterval: NodeJS.Timeout

  constructor() {
    super()
    
    // 定期清理超时的传输
    this.cleanupInterval = setInterval(() => {
      this.cleanupTimeoutTransfers()
    }, 10000)
  }

  /**
   * 开始文件上传
   */
  startUpload(config: FileTransferConfig): string {
    const transfer: ActiveFileTransfer = {
      id: config.fileId,
      config,
      progress: {
        fileId: config.fileId,
        fileName: config.fileName,
        totalBytes: config.fileSize,
        transferredBytes: 0,
        percentage: 0,
        speed: 0,
        estimatedTimeRemaining: 0
      },
      chunks: [],
      startTime: Date.now(),
      lastActivity: Date.now()
    }

    this.activeTransfers.set(config.fileId, transfer)
    
    this.emit('uploadStarted', {
      fileId: config.fileId,
      fileName: config.fileName,
      fileSize: config.fileSize
    })

    return config.fileId
  }

  /**
   * 发送文件块
   */
  sendFileChunk(fileId: string, chunk: Buffer): void {
    const message: FileMessage = {
      id: this.generateMessageId(),
      type: ProtocolType.FILE,
      timestamp: Date.now(),
      priority: 'normal',
      source: 'renderer', // TODO: 从配置获取
      operation: 'upload',
      fileId,
      chunk
    }

    this.emit('sendChunk', message)
  }

  /**
   * 完成文件传输
   */
  completeTransfer(fileId: string): void {
    const transfer = this.activeTransfers.get(fileId)
    if (!transfer) {
      return
    }

    // 计算校验和
    const hash = createHash('md5')
    for (const chunk of transfer.chunks) {
      hash.update(chunk)
    }
    const checksum = hash.digest('hex')

    const message: FileMessage = {
      id: this.generateMessageId(),
      type: ProtocolType.FILE,
      timestamp: Date.now(),
      priority: 'normal',
      source: 'renderer', // TODO: 从配置获取
      operation: 'complete',
      fileId,
      fileName: transfer.config.fileName,
      fileSize: transfer.config.fileSize
    }

    this.emit('completeTransfer', message)
    this.closeTransfer(fileId)
  }

  /**
   * 取消文件传输
   */
  cancelTransfer(fileId: string, reason?: string): void {
    const message: FileMessage = {
      id: this.generateMessageId(),
      type: ProtocolType.FILE,
      timestamp: Date.now(),
      priority: 'normal',
      source: 'renderer', // TODO: 从配置获取
      operation: 'error',
      fileId,
      error: reason || 'Transfer cancelled'
    }

    this.emit('cancelTransfer', message)
    this.closeTransfer(fileId)
  }

  /**
   * 获取传输进度
   */
  getTransferProgress(fileId: string): FileTransferProgress | undefined {
    const transfer = this.activeTransfers.get(fileId)
    return transfer?.progress
  }

  /**
   * 获取所有活跃传输
   */
  getActiveTransfers(): FileTransferProgress[] {
    return Array.from(this.activeTransfers.values()).map(transfer => transfer.progress)
  }

  /**
   * 检查是否可以处理消息
   */
  canHandle(message: IPCMessage): message is FileMessage {
    return message.type === ProtocolType.FILE
  }

  /**
   * 处理消息
   */
  async process(message: FileMessage, context: MessageContext): Promise<void> {
    switch (message.operation) {
      case 'upload':
        await this.handleUpload(message, context)
        break
      case 'download':
        await this.handleDownload(message, context)
        break
      case 'progress':
        await this.handleProgress(message, context)
        break
      case 'complete':
        await this.handleComplete(message, context)
        break
      case 'error':
        await this.handleError(message, context)
        break
    }
  }

  /**
   * 序列化数据
   */
  serialize(data: any): any {
    if (Buffer.isBuffer(data)) {
      return {
        type: 'Buffer',
        data: Array.from(data)
      }
    }
    return data
  }

  /**
   * 反序列化数据
   */
  deserialize(data: any): any {
    if (data && data.type === 'Buffer' && Array.isArray(data.data)) {
      return Buffer.from(data.data)
    }
    return data
  }

  // ============= 私有方法 =============

  private async handleUpload(message: FileMessage, context: MessageContext): Promise<void> {
    let transfer = this.activeTransfers.get(message.fileId)
    
    if (!transfer && message.fileName && message.fileSize) {
      // 创建新的传输
      const config: FileTransferConfig = {
        fileId: message.fileId,
        fileName: message.fileName,
        fileSize: message.fileSize,
        chunkSize: this.defaultChunkSize
      }
      
      transfer = {
        id: message.fileId,
        config,
        progress: {
          fileId: message.fileId,
          fileName: message.fileName,
          totalBytes: message.fileSize,
          transferredBytes: 0,
          percentage: 0,
          speed: 0,
          estimatedTimeRemaining: 0
        },
        chunks: [],
        startTime: Date.now(),
        lastActivity: Date.now()
      }
      
      this.activeTransfers.set(message.fileId, transfer)
    }

    if (!transfer) {
      this.emit('transferError', {
        fileId: message.fileId,
        error: 'Transfer not found'
      })
      return
    }

    if (message.chunk) {
      const chunk = this.deserialize(message.chunk) as Buffer
      transfer.chunks.push(chunk)
      transfer.lastActivity = Date.now()
      
      // 更新进度
      transfer.progress.transferredBytes += chunk.length
      transfer.progress.percentage = (transfer.progress.transferredBytes / transfer.progress.totalBytes) * 100
      
      // 计算传输速度
      const elapsed = Date.now() - transfer.startTime
      transfer.progress.speed = transfer.progress.transferredBytes / (elapsed / 1000)
      
      // 估算剩余时间
      const remaining = transfer.progress.totalBytes - transfer.progress.transferredBytes
      transfer.progress.estimatedTimeRemaining = remaining / transfer.progress.speed

      this.emit('uploadProgress', {
        fileId: message.fileId,
        progress: transfer.progress
      })
    }
  }

  private async handleDownload(message: FileMessage, context: MessageContext): Promise<void> {
    // 处理下载请求
    this.emit('downloadRequested', {
      fileId: message.fileId,
      fileName: message.fileName
    })
  }

  private async handleProgress(message: FileMessage, context: MessageContext): Promise<void> {
    if (message.progress !== undefined) {
      this.emit('progressUpdate', {
        fileId: message.fileId,
        progress: message.progress
      })
    }
  }

  private async handleComplete(message: FileMessage, context: MessageContext): Promise<void> {
    const transfer = this.activeTransfers.get(message.fileId)
    if (transfer) {
      // 验证文件完整性
      const hash = createHash('md5')
      for (const chunk of transfer.chunks) {
        hash.update(chunk)
      }
      const checksum = hash.digest('hex')
      
      this.emit('transferComplete', {
        fileId: message.fileId,
        fileName: transfer.config.fileName,
        fileSize: transfer.progress.transferredBytes,
        checksum,
        duration: Date.now() - transfer.startTime
      })
      
      this.closeTransfer(message.fileId)
    }
  }

  private async handleError(message: FileMessage, context: MessageContext): Promise<void> {
    this.emit('transferError', {
      fileId: message.fileId,
      error: message.error
    })
    
    this.closeTransfer(message.fileId)
  }

  private closeTransfer(fileId: string): void {
    const transfer = this.activeTransfers.get(fileId)
    if (transfer) {
      this.activeTransfers.delete(fileId)
      
      this.emit('transferClosed', {
        fileId,
        fileName: transfer.config.fileName,
        duration: Date.now() - transfer.startTime
      })
    }
  }

  private cleanupTimeoutTransfers(): void {
    const now = Date.now()
    const timeoutTransfers: string[] = []

    for (const [fileId, transfer] of this.activeTransfers) {
      if (now - transfer.lastActivity > this.transferTimeout) {
        timeoutTransfers.push(fileId)
      }
    }

    for (const fileId of timeoutTransfers) {
      this.emit('transferTimeout', { fileId })
      this.closeTransfer(fileId)
    }
  }

  private generateMessageId(): string {
    return `file-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 设置传输超时时间
   */
  setTransferTimeout(timeout: number): void {
    this.transferTimeout = timeout
  }

  /**
   * 设置默认块大小
   */
  setDefaultChunkSize(size: number): void {
    this.defaultChunkSize = size
  }

  /**
   * 清理资源
   */
  dispose(): void {
    // 关闭所有活跃传输
    for (const fileId of this.activeTransfers.keys()) {
      this.closeTransfer(fileId)
    }

    // 清理定时器
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
    }

    this.removeAllListeners()
  }
}
