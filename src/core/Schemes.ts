import { protocol } from 'electron'
import { PROXY, PROXY_UPDATER } from './Configure'

export const registerSchemesPrivileged = () => {
	protocol.registerSchemesAsPrivileged([
		{
			scheme: 'app', // TODO 需要确认是否有效，并且需调整名称 用以区分主框架/业务
			privileges: {
				standard: true,
				secure: true,
				supportFetchAPI: true
			}
		},
		{
			scheme: PROXY_UPDATER,
			privileges: {
				standard: true,
				secure: true,
				supportFetchAPI: true
			}
		},
		{
			scheme: PROXY,
			privileges: {
				standard: true,
				secure: true,
				supportFetchAPI: true
			}
		}
	])
}
