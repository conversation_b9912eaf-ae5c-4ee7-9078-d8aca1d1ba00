# Electron IPC 通信架构指南

一个高效、类型安全、可扩展的 Electron 进程间通信解决方案。

## 🚀 特性

- **🔒 类型安全**: 完整的 TypeScript 支持，编译时类型检查
- **🔌 可插拔协议**: 支持 RPC、事件、流式传输、文件传输等多种通信模式
- **⚡ 高性能**: 内置消息队列、连接池和性能监控
- **🛡️ 错误处理**: 完善的错误处理、重试机制和熔断器
- **📊 监控调试**: 实时性能监控、调试工具和日志系统
- **🎯 装饰器支持**: 简化方法注册和事件监听
- **🔄 自动重连**: 网络中断时自动重连机制

## 📦 安装

```bash
npm install @your-org/electron-ipc-architecture
```

## 🏗️ 架构概览

```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Application Layer)                │
├─────────────────────────────────────────────────────────────┤
│              类型安全 API (Type-Safe APIs)                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ RPC Client  │ │Event Client │ │Stream Client│ │File API │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────┤
│                协议适配层 (Protocol Adapters)               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ RPC Adapter │ │Event Adapter│ │Stream Adapter│ │File Adap│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────┤
│                核心通信层 (Core Communication)              │
│              ┌─────────────────────────────────┐             │
│              │    Communication Manager        │             │
│              │  ┌─────────┐ ┌─────────────────┐ │             │
│              │  │ Message │ │ Error Handler   │ │             │
│              │  │ Queue   │ │ & Retry Logic   │ │             │
│              │  └─────────┘ └─────────────────┘ │             │
│              └─────────────────────────────────┘             │
├─────────────────────────────────────────────────────────────┤
│                监控调试层 (Monitoring & Debug)              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐             │
│  │   Logger    │ │   Metrics   │ │ Debug Tools │             │
│  └─────────────┘ └─────────────┘ └─────────────┘             │
├─────────────────────────────────────────────────────────────┤
│                传输层 (Transport Layer)                     │
│              ┌─────────────────────────────────┐             │
│              │         Electron IPC            │             │
│              │  (ipcMain / ipcRenderer)        │             │
│              └─────────────────────────────────┘             │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 快速开始

### 1. 主进程设置

```typescript
// main.ts
import { createIPCServer } from '../src/core/ipc'

const ipcServer = createIPCServer({
  processType: 'main',
  enableLogging: true,
  enableMetrics: true
})

// 注册方法
ipcServer.registerMethod('getSystemInfo', async () => {
  const os = await import('os')
  return {
    platform: os.platform(),
    arch: os.arch(),
    memory: os.totalmem()
  }
})

// 注册窗口
ipcServer.registerWindow(mainWindow)
```

### 2. 预加载脚本

```typescript
// preload.ts
import { exposeBridgeAPI } from '../src/core/ipc'

// 暴露安全的 IPC API 到渲染进程
exposeBridgeAPI()
```

### 3. 渲染进程使用

```typescript
// renderer.ts
// 调用主进程方法
const systemInfo = await window.ipcBridge.call('getSystemInfo')
console.log('System info:', systemInfo)

// 订阅事件
const subscription = window.ipcBridge.subscribe('systemUpdate', (data) => {
  console.log('System updated:', data)
})

// 上传文件
const fileId = await window.ipcBridge.uploadFile(file, {
  onProgress: (progress) => console.log(`Progress: ${progress}%`)
})
```

## 📚 详细使用指南

### RPC 调用

```typescript
// 类型安全的 RPC 调用
interface MyRPCMethods extends RPCMethodMap {
  'user.create': {
    params: [{ name: string; email: string }]
    result: { id: string; name: string; email: string }
  }
}

const typedRPC = createTypedRPCClient<MyRPCMethods>(ipcClient)
const user = await typedRPC.call('user.create', [{ name: 'John', email: '<EMAIL>' }])
```

### 事件系统

```typescript
// 发布事件
ipcClient.publish('userCreated', { id: '123', name: 'John' })

// 订阅事件
const subscription = ipcClient.subscribe('userCreated', (data) => {
  console.log('New user:', data)
}, {
  namespace: 'users',
  once: false,
  priority: 'high'
})
```

### 流式传输

```typescript
// 创建数据流
const streamId = ipcClient.createStream({
  streamId: 'data-export',
  bufferSize: 1000,
  timeout: 30000
}, (chunk, isEnd) => {
  if (isEnd) {
    console.log('Stream ended')
  } else {
    console.log('Received chunk:', chunk)
  }
})

// 发送数据块
ipcClient.sendStreamChunk(streamId, { data: 'chunk1' })
```

### 文件传输

```typescript
// 上传文件
const fileId = await ipcClient.uploadFile(file, {
  chunkSize: 64 * 1024,
  onProgress: (progress) => {
    console.log(`Upload progress: ${progress}%`)
  }
})

// 下载文件
const downloadId = await ipcServer.downloadFile(window, '/path/to/file.pdf', {
  onProgress: (progress) => {
    console.log(`Download progress: ${progress}%`)
  }
})
```

## 🎯 装饰器支持

```typescript
class UserService {
  @RPCMethod({ timeout: 5000, retries: 3 })
  @withPerformanceMonitoring()
  @withErrorHandling()
  async createUser(userData: any) {
    // 实现用户创建逻辑
  }

  @EventListener('user.updated')
  onUserUpdated(data: any) {
    // 处理用户更新事件
  }
}

// 自动注册所有装饰器标记的方法
autoRegisterMethods(new UserService(), ipcServer)
```

## 📊 监控和调试

```typescript
// 启用调试模式
const debugTools = new DebugTools(logger, metrics)
debugTools.enable()

// 获取性能统计
const stats = debugTools.getPerformanceStats()
console.log('Average response time:', stats.averageResponseTime)

// 生成调试报告
const report = debugTools.generateReport()
console.log(report)
```

## ⚙️ 配置选项

```typescript
const config = {
  processType: 'main' | 'renderer',
  enableLogging: true,
  enableMetrics: true,
  defaultTimeout: 30000,
  maxRetries: 3,
  retryDelay: 1000,
  enableCompression: false,
  enableEncryption: false
}
```

## 🛡️ 错误处理

```typescript
// 自定义错误处理
const errorHandler = new ErrorHandler({
  enableRetry: true,
  enableCircuitBreaker: true,
  retryConfig: {
    maxRetries: 3,
    baseDelay: 1000,
    backoffFactor: 2
  },
  circuitBreakerConfig: {
    failureThreshold: 5,
    resetTimeout: 60000
  }
})

// 使用错误处理装饰器
@withErrorHandling(errorHandler)
async function riskyOperation() {
  // 可能失败的操作
}
```

## 📈 性能优化

- **消息队列**: 自动批处理和优先级排序
- **连接池**: 复用连接减少开销
- **压缩**: 可选的消息压缩
- **缓存**: 智能响应缓存
- **监控**: 实时性能指标

## 🔧 开发工具

- **调试面板**: 实时消息跟踪
- **性能分析**: 响应时间和吞吐量统计
- **错误监控**: 详细的错误报告和堆栈跟踪
- **日志系统**: 结构化日志记录

## 📝 示例项目

查看 `examples/` 目录获取完整的示例项目：

- `basic-usage.ts` - 基础使用示例
- `advanced-usage.ts` - 高级功能示例

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
